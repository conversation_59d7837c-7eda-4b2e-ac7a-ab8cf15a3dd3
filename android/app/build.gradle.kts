plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    // Firebase插件（条件应用，避免构建错误）
    // id("com.google.gms.google-services") apply false
    // id("com.google.firebase.crashlytics") apply false
    // id("com.google.firebase.firebase-perf") apply false
}

android {
    namespace = "com.company.enterprise_flutter"
    compileSdk = 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    defaultConfig {
        applicationId = "com.company.enterprise_flutter"
        minSdk = 21
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // 企业级配置
        multiDexEnabled = true
        vectorDrawables.useSupportLibrary = true

        // 测试配置
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // ProGuard配置文件
        proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    }

    buildTypes {
        debug {
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
        }

        release {
            isDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }

        // 企业级profile构建类型（如果不存在则创建）
        getByName("profile") {
            isDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = false
            applicationIdSuffix = ".profile"
            versionNameSuffix = "-profile"
        }
    }

    flavorDimensions += "environment"
    productFlavors {
        create("development") {
            dimension = "environment"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
            manifestPlaceholders["appName"] = "Flutter企业版(开发)"
        }

        create("staging") {
            dimension = "environment"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            manifestPlaceholders["appName"] = "Flutter企业版(测试)"
        }

        create("production") {
            dimension = "environment"
            manifestPlaceholders["appName"] = "Flutter企业版"
        }
    }


}

// 企业级依赖管理
dependencies {
    // AndroidX核心库
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.activity:activity-compose:1.8.2")

    // 多DEX支持
    implementation("androidx.multidex:multidex:2.0.1")

    // 网络安全
    implementation("androidx.security:security-crypto:1.1.0-alpha06")

    // 测试依赖
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}

flutter {
    source = "../.."
}
