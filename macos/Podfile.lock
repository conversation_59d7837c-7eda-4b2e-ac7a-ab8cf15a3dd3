PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - sqlite3 (3.50.2):
    - sqlite3/common (= 3.50.2)
  - sqlite3/common (3.50.2)
  - sqlite3/dbstatvtab (3.50.2):
    - sqlite3/common
  - sqlite3/fts5 (3.50.2):
    - sqlite3/common
  - sqlite3/math (3.50.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.50.2):
    - sqlite3/common
  - sqlite3/rtree (3.50.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.50.1)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift
    - sqlite3

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin

SPEC CHECKSUMS:
  connectivity_plus: e74b9f74717d2d99d45751750e266e55912baeb5
  flutter_secure_storage_macos: 7f45e30f838cf2659862a4e4e3ee1c347c2b3b54
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  sqlite3: 3e82a2daae39ba3b41ae6ee84a130494585460fc
  sqlite3_flutter_libs: e7fc8c9ea2200ff3271f08f127842131746b70e2

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
