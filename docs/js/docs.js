// 文档内容数据
const docContent = {
    'getting-started': `
        <div class="container-fluid">
            <h2><i class="fas fa-rocket"></i> 快速开始</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#prerequisites">环境要求</a></li>
                    <li><a href="#installation">安装步骤</a></li>
                    <li><a href="#first-run">首次运行</a></li>
                    <li><a href="#project-structure">项目结构</a></li>
                </ul>
            </div>

            <h3 id="prerequisites">环境要求</h3>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 系统要求</h6>
                <ul class="mb-0">
                    <li>Flutter SDK 3.10.0 或更高版本</li>
                    <li>Dart SDK 3.0.0 或更高版本</li>
                    <li>Android Studio 或 VS Code</li>
                    <li>Git 版本控制</li>
                </ul>
            </div>

            <h3 id="installation">安装步骤</h3>
            <div class="code-block">
                <h6>1. 克隆项目</h6>
                <pre><code class="language-bash">git clone https://github.com/your-org/flutter-enterprise-template.git
cd flutter-enterprise-template</code></pre>
            </div>

            <div class="code-block">
                <h6>2. 安装依赖</h6>
                <pre><code class="language-bash">flutter pub get</code></pre>
            </div>

            <div class="code-block">
                <h6>3. 生成代码</h6>
                <pre><code class="language-bash">flutter packages pub run build_runner build</code></pre>
            </div>

            <h3 id="first-run">首次运行</h3>
            <div class="code-block">
                <h6>运行应用</h6>
                <pre><code class="language-bash"># 开发环境
flutter run --flavor dev

# 生产环境
flutter run --flavor prod</code></pre>
            </div>

            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle"></i> 成功运行</h6>
                <p class="mb-0">如果一切正常，您应该看到应用启动并显示主页面，包含架构特性和功能状态的展示。</p>
            </div>

            <h3 id="project-structure">项目结构</h3>
            <div class="code-block">
                <pre><code class="language-text">lib/
├── core/                    # 核心模块
│   ├── config/             # 配置管理
│   ├── di/                 # 依赖注入
│   ├── navigation/         # 路由管理
│   ├── network/            # 网络层
│   └── ...
├── features/               # 功能模块
│   ├── auth/              # 认证模块
│   ├── theming/           # 主题模块
│   └── ...
├── shared/                # 共享组件
│   ├── pages/             # 共享页面
│   └── widgets/           # 通用组件
└── main.dart              # 应用入口</code></pre>
            </div>
        </div>
    `,

    'architecture': `
        <div class="container-fluid">
            <h2><i class="fas fa-sitemap"></i> 架构设计</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#clean-architecture">Clean Architecture</a></li>
                    <li><a href="#layer-details">层级详解</a></li>
                    <li><a href="#dependency-flow">依赖流向</a></li>
                    <li><a href="#module-system">模块化系统</a></li>
                </ul>
            </div>

            <h3 id="clean-architecture">Clean Architecture</h3>
            <p>本项目采用Clean Architecture（整洁架构）设计模式，将应用分为三个主要层级：</p>

            <div class="architecture-diagram">
                <div class="layer presentation">
                    <h6><i class="fas fa-mobile-alt"></i> 表现层 (Presentation)</h6>
                    <p>UI组件、BLoC、页面</p>
                </div>
                <div style="margin: 1rem 0;">
                    <i class="fas fa-arrow-down fa-2x text-muted"></i>
                </div>
                <div class="layer domain">
                    <h6><i class="fas fa-business-time"></i> 领域层 (Domain)</h6>
                    <p>业务逻辑、实体、用例</p>
                </div>
                <div style="margin: 1rem 0;">
                    <i class="fas fa-arrow-down fa-2x text-muted"></i>
                </div>
                <div class="layer data">
                    <h6><i class="fas fa-database"></i> 数据层 (Data)</h6>
                    <p>Repository、数据源、模型</p>
                </div>
            </div>

            <h3 id="layer-details">层级详解</h3>

            <div class="row">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6><i class="fas fa-mobile-alt"></i> 表现层</h6>
                        </div>
                        <div class="card-body">
                            <h6>职责：</h6>
                            <ul>
                                <li>处理用户界面</li>
                                <li>管理应用状态</li>
                                <li>响应用户交互</li>
                            </ul>
                            <h6>组件：</h6>
                            <ul>
                                <li>Pages (页面)</li>
                                <li>Widgets (组件)</li>
                                <li>BLoC (状态管理)</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h6><i class="fas fa-business-time"></i> 领域层</h6>
                        </div>
                        <div class="card-body">
                            <h6>职责：</h6>
                            <ul>
                                <li>定义业务规则</li>
                                <li>处理业务逻辑</li>
                                <li>独立于外部框架</li>
                            </ul>
                            <h6>组件：</h6>
                            <ul>
                                <li>Entities (实体)</li>
                                <li>Use Cases (用例)</li>
                                <li>Repository Interfaces</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h6><i class="fas fa-database"></i> 数据层</h6>
                        </div>
                        <div class="card-body">
                            <h6>职责：</h6>
                            <ul>
                                <li>数据获取和存储</li>
                                <li>外部API调用</li>
                                <li>数据转换和映射</li>
                            </ul>
                            <h6>组件：</h6>
                            <ul>
                                <li>Repository Implementations</li>
                                <li>Data Sources</li>
                                <li>Models & DTOs</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <h3 id="dependency-flow">依赖流向</h3>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 依赖规则</h6>
                <p>依赖关系只能从外层指向内层，内层不能依赖外层：</p>
                <ul class="mb-0">
                    <li><strong>表现层</strong> → 领域层</li>
                    <li><strong>数据层</strong> → 领域层</li>
                    <li><strong>领域层</strong> → 独立存在</li>
                </ul>
            </div>

            <h3 id="module-system">模块化系统</h3>
            <p>项目采用模块化设计，支持功能的动态启用和禁用：</p>

            <div class="code-block">
                <h6>功能配置示例</h6>
                <pre><code class="language-yaml"># app_config.yaml
features:
  authentication: true      # 用户认证
  authorization: true       # 权限管理
  internationalization: true # 国际化
  theming: true            # 主题管理
  analytics: false         # 数据分析（禁用）</code></pre>
            </div>
        </div>
    `,

    'features': `
        <div class="container-fluid">
            <h2><i class="fas fa-cogs"></i> 功能模块</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#core-features">核心功能</a></li>
                    <li><a href="#auth-module">认证模块</a></li>
                    <li><a href="#theme-module">主题模块</a></li>
                    <li><a href="#i18n-module">国际化模块</a></li>
                    <li><a href="#config-system">配置系统</a></li>
                </ul>
            </div>

            <h3 id="core-features">核心功能</h3>
            <div class="row">
                <div class="col-lg-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-shield-alt text-primary"></i> 用户认证</h5>
                        <p>完整的用户认证系统，支持登录、注册、密码重置等功能。</p>
                        <div class="mt-3">
                            <span class="badge bg-success">已启用</span>
                            <span class="badge bg-secondary">Clean Architecture</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-key text-warning"></i> 权限管理</h5>
                        <p>基于角色的权限控制系统，支持细粒度的权限管理。</p>
                        <div class="mt-3">
                            <span class="badge bg-success">已启用</span>
                            <span class="badge bg-secondary">依赖认证</span>
                        </div>
                    </div>
                </div>
            </div>

            <h3 id="auth-module">认证模块</h3>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 模块路径</h6>
                <code>lib/features/auth/</code>
            </div>

            <div class="code-block">
                <h6>认证实体</h6>
                <pre><code class="language-dart">class User extends Equatable {
  const User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.roles = const [],
    this.permissions = const [],
  });

  final String id;
  final String email;
  final String name;
  final String? avatar;
  final List<String> roles;
  final List<String> permissions;

  bool hasRole(String role) => roles.contains(role);
  bool hasPermission(String permission) => permissions.contains(permission);
}</code></pre>
            </div>

            <h3 id="theme-module">主题模块</h3>
            <p>支持动态主题切换，包含亮色、暗色和系统主题。</p>

            <div class="code-block">
                <h6>主题配置</h6>
                <pre><code class="language-dart">class AppTheme {
  final String id;
  final String name;
  final ColorScheme lightColorScheme;
  final ColorScheme darkColorScheme;

  const AppTheme({
    required this.id,
    required this.name,
    required this.lightColorScheme,
    required this.darkColorScheme,
  });
}</code></pre>
            </div>

            <h3 id="i18n-module">国际化模块</h3>
            <p>支持多语言切换，包含中文、英文和日文。</p>

            <div class="code-block">
                <h6>本地化使用</h6>
                <pre><code class="language-dart">class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Text(l10n?.welcomeTitle ?? '欢迎');
  }
}</code></pre>
            </div>

            <h3 id="config-system">配置系统</h3>
            <p>模块化配置系统，支持功能的动态启用和禁用。</p>

            <div class="code-block">
                <h6>配置检查</h6>
                <pre><code class="language-dart">final featureConfig = get<FeatureConfig>();

if (featureConfig.isFeatureEnabled('authentication')) {
  // 显示认证相关UI
} else {
  // 隐藏或禁用认证功能
}</code></pre>
            </div>
        </div>
    `,

    'api': `
        <div class="container-fluid">
            <h2><i class="fas fa-code"></i> API文档</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#core-apis">核心API</a></li>
                    <li><a href="#auth-apis">认证API</a></li>
                    <li><a href="#config-apis">配置API</a></li>
                    <li><a href="#theme-apis">主题API</a></li>
                </ul>
            </div>

            <h3 id="core-apis">核心API</h3>

            <h4>依赖注入</h4>
            <div class="code-block">
                <pre><code class="language-dart">// 获取服务实例
T get<T extends Object>({String? instanceName});

// 注册单例服务
void registerSingleton<T extends Object>(T instance);

// 注册懒加载单例
void registerLazySingleton<T extends Object>(FactoryFunc<T> factoryFunc);

// 检查服务是否已注册
bool isRegistered<T extends Object>({String? instanceName});</code></pre>
            </div>

            <h4>路由管理</h4>
            <div class="code-block">
                <pre><code class="language-dart">class AppRouter {
  // 导航到指定路由
  void go(String location);

  // 推送新路由
  void push(String location);

  // 替换当前路由
  void replace(String location);

  // 返回上一页
  void pop();

  // 检查是否可以返回
  bool canPop();
}</code></pre>
            </div>

            <h3 id="auth-apis">认证API</h3>

            <h4>认证服务</h4>
            <div class="code-block">
                <pre><code class="language-dart">abstract class IAuthService {
  // 用户登录
  Future<AuthResult> login(String email, String password);

  // 用户注册
  Future<AuthResult> register(String name, String email, String password);

  // 用户登出
  Future<void> logout();

  // 获取当前用户
  Future<User?> getCurrentUser();

  // 检查登录状态
  bool isLoggedIn();
}</code></pre>
            </div>

            <h3 id="config-apis">配置API</h3>

            <h4>功能配置</h4>
            <div class="code-block">
                <pre><code class="language-dart">class FeatureConfig {
  // 检查功能是否启用
  bool isFeatureEnabled(String featureName);

  // 动态启用功能
  Future<bool> enableFeature(String featureName);

  // 动态禁用功能
  Future<bool> disableFeature(String featureName);

  // 获取所有启用的功能
  List<String> getEnabledFeatures();

  // 获取功能依赖关系
  List<String> getFeatureDependencies(String featureName);

  // 功能配置变更事件流
  Stream<FeatureConfigChangeEvent> get onConfigChanged;
}</code></pre>
            </div>

            <h3 id="theme-apis">主题API</h3>

            <h4>主题管理</h4>
            <div class="code-block">
                <pre><code class="language-dart">// 主题事件
abstract class ThemeEvent extends Equatable {
  const ThemeEvent();
}

class LoadThemeEvent extends ThemeEvent {
  const LoadThemeEvent();
}

class ThemeModeChangedEvent extends ThemeEvent {
  const ThemeModeChangedEvent(this.themeMode);
  final ThemeMode themeMode;
}

// 主题状态
abstract class ThemeState extends Equatable {
  ThemeData get lightTheme;
  ThemeData get darkTheme;
  ThemeMode get themeMode;
}</code></pre>
            </div>
        </div>
    `,

    'development': `
        <div class="container-fluid">
            <h2><i class="fas fa-tools"></i> 开发指南</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#dev-setup">开发环境设置</a></li>
                    <li><a href="#coding-standards">编码规范</a></li>
                    <li><a href="#testing">测试指南</a></li>
                    <li><a href="#debugging">调试技巧</a></li>
                    <li><a href="#performance">性能优化</a></li>
                </ul>
            </div>

            <h3 id="dev-setup">开发环境设置</h3>

            <h4>VS Code 配置</h4>
            <div class="code-block">
                <h6>推荐扩展</h6>
                <pre><code class="language-json">{
  "recommendations": [
    "dart-code.flutter",
    "dart-code.dart-code",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "usernamehw.errorlens"
  ]
}</code></pre>
            </div>

            <h4>代码生成</h4>
            <div class="code-block">
                <pre><code class="language-bash"># 监听文件变化并自动生成代码
flutter packages pub run build_runner watch

# 一次性生成代码
flutter packages pub run build_runner build

# 清理并重新生成
flutter packages pub run build_runner build --delete-conflicting-outputs</code></pre>
            </div>

            <h3 id="coding-standards">编码规范</h3>

            <h4>命名规范</h4>
            <div class="alert alert-info">
                <ul class="mb-0">
                    <li><strong>类名</strong>: PascalCase (例: UserRepository)</li>
                    <li><strong>方法名</strong>: camelCase (例: getUserById)</li>
                    <li><strong>变量名</strong>: camelCase (例: currentUser)</li>
                    <li><strong>常量</strong>: lowerCamelCase (例: maxRetryCount)</li>
                    <li><strong>文件名</strong>: snake_case (例: user_repository.dart)</li>
                </ul>
            </div>

            <h4>代码组织</h4>
            <div class="code-block">
                <pre><code class="language-dart">// 导入顺序：
// 1. Dart 核心库
import 'dart:async';
import 'dart:convert';

// 2. Flutter 框架
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. 第三方包
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

// 4. 项目内部导入
import '../domain/entities/user.dart';
import '../data/repositories/user_repository.dart';</code></pre>
            </div>

            <h3 id="testing">测试指南</h3>

            <h4>单元测试</h4>
            <div class="code-block">
                <pre><code class="language-dart">// test/features/auth/domain/use_cases/login_use_case_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('LoginUseCase', () {
    late LoginUseCase useCase;
    late MockAuthRepository mockRepository;

    setUp(() {
      mockRepository = MockAuthRepository();
      useCase = LoginUseCase(mockRepository);
    });

    test('should return user when login is successful', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const user = User(id: '1', email: email, name: 'Test User');

      when(mockRepository.login(email, password))
          .thenAnswer((_) async => Right(user));

      // Act
      final result = await useCase(LoginParams(email: email, password: password));

      // Assert
      expect(result, Right(user));
      verify(mockRepository.login(email, password));
    });
  });
}</code></pre>
            </div>

            <h4>Widget测试</h4>
            <div class="code-block">
                <pre><code class="language-dart">// test/features/auth/presentation/pages/login_page_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void main() {
  group('LoginPage', () {
    testWidgets('should display login form', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (_) => MockAuthBloc(),
            child: LoginPage(),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('登录'), findsOneWidget);
      expect(find.text('邮箱'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
    });
  });
}</code></pre>
            </div>

            <h3 id="debugging">调试技巧</h3>

            <h4>日志记录</h4>
            <div class="code-block">
                <pre><code class="language-dart">import 'package:flutter/foundation.dart';

// 开发环境日志
if (kDebugMode) {
  print('Debug: User logged in - \${user.email}');
}

// 使用logger包
import 'package:logger/logger.dart';

final logger = Logger();

logger.d('Debug message');
logger.i('Info message');
logger.w('Warning message');
logger.e('Error message');</code></pre>
            </div>

            <h4>性能分析</h4>
            <div class="code-block">
                <pre><code class="language-bash"># 性能分析
flutter run --profile

# 内存分析
flutter run --profile --trace-startup

# 构建分析
flutter build apk --analyze-size</code></pre>
            </div>

            <h3 id="performance">性能优化</h3>

            <h4>Widget优化</h4>
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> 性能建议</h6>
                <ul class="mb-0">
                    <li>使用const构造函数</li>
                    <li>避免在build方法中创建对象</li>
                    <li>合理使用RepaintBoundary</li>
                    <li>优化列表渲染性能</li>
                </ul>
            </div>

            <div class="code-block">
                <pre><code class="language-dart">// 好的做法
class MyWidget extends StatelessWidget {
  const MyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Text('Hello World');
  }
}

// 避免的做法
class BadWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final list = [1, 2, 3]; // 每次build都会创建新列表
    return Text('Count: \${list.length}');
  }
}</code></pre>
            </div>
        </div>
    `,

    'deployment': `
        <div class="container-fluid">
            <h2><i class="fas fa-cloud"></i> 部署指南</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#build-config">构建配置</a></li>
                    <li><a href="#android-deploy">Android部署</a></li>
                    <li><a href="#ios-deploy">iOS部署</a></li>
                    <li><a href="#ci-cd">CI/CD配置</a></li>
                </ul>
            </div>

            <h3 id="build-config">构建配置</h3>

            <h4>环境配置</h4>
            <div class="code-block">
                <pre><code class="language-bash"># 开发环境构建
flutter build apk --flavor dev --dart-define=ENVIRONMENT=dev

# 生产环境构建
flutter build apk --flavor prod --dart-define=ENVIRONMENT=prod --release</code></pre>
            </div>

            <h4>代码混淆</h4>
            <div class="code-block">
                <pre><code class="language-bash"># 启用代码混淆
flutter build apk --obfuscate --split-debug-info=build/debug-info</code></pre>
            </div>

            <h3 id="android-deploy">Android部署</h3>

            <h4>签名配置</h4>
            <div class="code-block">
                <h6>android/app/build.gradle</h6>
                <pre><code class="language-gradle">android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}</code></pre>
            </div>

            <h3 id="ios-deploy">iOS部署</h3>

            <h4>证书配置</h4>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> iOS部署要求</h6>
                <ul class="mb-0">
                    <li>Apple Developer账号</li>
                    <li>有效的开发证书</li>
                    <li>App Store Connect配置</li>
                    <li>TestFlight测试（可选）</li>
                </ul>
            </div>

            <div class="code-block">
                <pre><code class="language-bash"># iOS构建
flutter build ios --release

# 创建IPA文件
flutter build ipa --release</code></pre>
            </div>

            <h3 id="ci-cd">CI/CD配置</h3>

            <h4>GitHub Actions</h4>
            <div class="code-block">
                <h6>.github/workflows/build.yml</h6>
                <pre><code class="language-yaml">name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.10.0'
    - run: flutter pub get
    - run: flutter test
    - run: flutter analyze

  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.10.0'
    - run: flutter pub get
    - run: flutter build apk --release</code></pre>
            </div>
        </div>
    `,

    'examples': `
        <div class="container-fluid">
            <h2><i class="fas fa-lightbulb"></i> 示例代码</h2>

            <div class="toc">
                <h6>目录</h6>
                <ul>
                    <li><a href="#basic-examples">基础示例</a></li>
                    <li><a href="#advanced-examples">高级示例</a></li>
                    <li><a href="#integration-examples">集成示例</a></li>
                </ul>
            </div>

            <h3 id="basic-examples">基础示例</h3>

            <h4>创建新功能模块</h4>
            <div class="code-block">
                <h6>1. 创建实体</h6>
                <pre><code class="language-dart">// lib/features/todo/domain/entities/todo.dart
import 'package:equatable/equatable.dart';

class Todo extends Equatable {
  const Todo({
    required this.id,
    required this.title,
    required this.description,
    this.isCompleted = false,
    required this.createdAt,
  });

  final String id;
  final String title;
  final String description;
  final bool isCompleted;
  final DateTime createdAt;

  Todo copyWith({
    String? id,
    String? title,
    String? description,
    bool? isCompleted,
    DateTime? createdAt,
  }) {
    return Todo(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object> get props => [id, title, description, isCompleted, createdAt];
}</code></pre>
            </div>

            <div class="code-block">
                <h6>2. 创建Repository接口</h6>
                <pre><code class="language-dart">// lib/features/todo/domain/repositories/todo_repository.dart
import '../entities/todo.dart';

abstract class TodoRepository {
  Future<List<Todo>> getTodos();
  Future<Todo> getTodoById(String id);
  Future<Todo> createTodo(Todo todo);
  Future<Todo> updateTodo(Todo todo);
  Future<void> deleteTodo(String id);
}</code></pre>
            </div>

            <h4>使用BLoC状态管理</h4>
            <div class="code-block">
                <h6>BLoC实现</h6>
                <pre><code class="language-dart">// lib/features/todo/presentation/bloc/todo_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Events
abstract class TodoEvent extends Equatable {
  const TodoEvent();
  @override
  List<Object> get props => [];
}

class LoadTodosEvent extends TodoEvent {}

class AddTodoEvent extends TodoEvent {
  const AddTodoEvent(this.todo);
  final Todo todo;
  @override
  List<Object> get props => [todo];
}

// States
abstract class TodoState extends Equatable {
  const TodoState();
  @override
  List<Object> get props => [];
}

class TodoInitial extends TodoState {}

class TodoLoading extends TodoState {}

class TodoLoaded extends TodoState {
  const TodoLoaded(this.todos);
  final List<Todo> todos;
  @override
  List<Object> get props => [todos];
}

class TodoError extends TodoState {
  const TodoError(this.message);
  final String message;
  @override
  List<Object> get props => [message];
}

// BLoC
class TodoBloc extends Bloc<TodoEvent, TodoState> {
  TodoBloc(this._repository) : super(TodoInitial()) {
    on<LoadTodosEvent>(_onLoadTodos);
    on<AddTodoEvent>(_onAddTodo);
  }

  final TodoRepository _repository;

  Future<void> _onLoadTodos(LoadTodosEvent event, Emitter<TodoState> emit) async {
    emit(TodoLoading());
    try {
      final todos = await _repository.getTodos();
      emit(TodoLoaded(todos));
    } catch (e) {
      emit(TodoError(e.toString()));
    }
  }

  Future<void> _onAddTodo(AddTodoEvent event, Emitter<TodoState> emit) async {
    try {
      await _repository.createTodo(event.todo);
      add(LoadTodosEvent());
    } catch (e) {
      emit(TodoError(e.toString()));
    }
  }
}</code></pre>
            </div>

            <h3 id="advanced-examples">高级示例</h3>

            <h4>自定义依赖注入</h4>
            <div class="code-block">
                <pre><code class="language-dart">// lib/features/todo/di/todo_injection.dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

@module
abstract class TodoModule {
  @lazySingleton
  TodoRepository get todoRepository => TodoRepositoryImpl(
    get<TodoRemoteDataSource>(),
    get<TodoLocalDataSource>(),
  );

  @lazySingleton
  TodoRemoteDataSource get todoRemoteDataSource => TodoRemoteDataSourceImpl(get<Dio>());

  @lazySingleton
  TodoLocalDataSource get todoLocalDataSource => TodoLocalDataSourceImpl(get<Database>());

  @factory
  TodoBloc get todoBloc => TodoBloc(get<TodoRepository>());
}</code></pre>
            </div>

            <h4>网络请求封装</h4>
            <div class="code-block">
                <pre><code class="language-dart">// lib/features/todo/data/data_sources/todo_remote_data_source.dart
import 'package:dio/dio.dart';
import '../models/todo_model.dart';

abstract class TodoRemoteDataSource {
  Future<List<TodoModel>> getTodos();
  Future<TodoModel> createTodo(TodoModel todo);
}

class TodoRemoteDataSourceImpl implements TodoRemoteDataSource {
  const TodoRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<List<TodoModel>> getTodos() async {
    try {
      final response = await _dio.get('/todos');
      return (response.data as List)
          .map((json) => TodoModel.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    }
  }

  @override
  Future<TodoModel> createTodo(TodoModel todo) async {
    try {
      final response = await _dio.post('/todos', data: todo.toJson());
      return TodoModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Network error');
    }
  }
}</code></pre>
            </div>

            <h3 id="integration-examples">集成示例</h3>

            <h4>完整页面实现</h4>
            <div class="code-block">
                <pre><code class="language-dart">// lib/features/todo/presentation/pages/todo_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TodoListPage extends StatelessWidget {
  const TodoListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => get<TodoBloc>()..add(LoadTodosEvent()),
      child: const _TodoListView(),
    );
  }
}

class _TodoListView extends StatelessWidget {
  const _TodoListView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('待办事项'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddTodoDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<TodoBloc, TodoState>(
        builder: (context, state) {
          if (state is TodoLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is TodoLoaded) {
            return ListView.builder(
              itemCount: state.todos.length,
              itemBuilder: (context, index) {
                final todo = state.todos[index];
                return ListTile(
                  title: Text(todo.title),
                  subtitle: Text(todo.description),
                  trailing: Checkbox(
                    value: todo.isCompleted,
                    onChanged: (value) {
                      // 更新待办事项状态
                    },
                  ),
                );
              },
            );
          } else if (state is TodoError) {
            return Center(child: Text('错误: \${state.message}'));
          }
          return const Center(child: Text('暂无数据'));
        },
      ),
    );
  }

  void _showAddTodoDialog(BuildContext context) {
    // 显示添加待办事项对话框
  }
}</code></pre>
            </div>
        </div>
    `,

    'faq': `
        <div class="container-fluid">
            <h2><i class="fas fa-question-circle"></i> 常见问题</h2>

            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            如何添加新的功能模块？
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>添加新功能模块需要遵循Clean Architecture的分层结构：</p>
                            <ol>
                                <li>在 <code>lib/features/</code> 下创建新的功能目录</li>
                                <li>按照 <code>data/</code>、<code>domain/</code>、<code>presentation/</code> 结构组织代码</li>
                                <li>在 <code>assets/config/app_config.yaml</code> 中配置功能开关</li>
                                <li>在依赖注入配置中注册相关服务</li>
                                <li>添加路由配置（如需要）</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            如何配置不同的环境？
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>项目支持多环境配置：</p>
                            <ul>
                                <li><strong>开发环境</strong>: <code>flutter run --dart-define=ENVIRONMENT=dev</code></li>
                                <li><strong>测试环境</strong>: <code>flutter run --dart-define=ENVIRONMENT=test</code></li>
                                <li><strong>生产环境</strong>: <code>flutter run --dart-define=ENVIRONMENT=prod</code></li>
                            </ul>
                            <p>环境配置文件位于 <code>lib/core/config/environment_config.dart</code></p>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            如何处理状态管理？
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>项目使用BLoC模式进行状态管理：</p>
                            <ul>
                                <li><strong>全局状态</strong>: 使用 <code>GlobalStateManager</code></li>
                                <li><strong>功能状态</strong>: 每个功能模块有独立的BLoC</li>
                                <li><strong>UI状态</strong>: 使用 <code>StatefulWidget</code> 或 <code>BlocBuilder</code></li>
                            </ul>
                            <p>状态管理遵循单向数据流原则，确保状态的可预测性。</p>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                            如何进行单元测试？
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>项目提供完整的测试框架：</p>
                            <ul>
                                <li><strong>单元测试</strong>: 测试业务逻辑和用例</li>
                                <li><strong>Widget测试</strong>: 测试UI组件</li>
                                <li><strong>集成测试</strong>: 测试完整功能流程</li>
                            </ul>
                            <p>运行测试命令：</p>
                            <pre><code>flutter test                    # 运行所有测试
flutter test test/unit/         # 运行单元测试
flutter test test/widget/       # 运行Widget测试</code></pre>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq5">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                            如何自定义主题？
                        </button>
                    </h2>
                    <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>主题系统支持完全自定义：</p>
                            <ol>
                                <li>在 <code>lib/features/theming/domain/entities/</code> 中定义主题实体</li>
                                <li>实现亮色和暗色配色方案</li>
                                <li>在主题配置中注册新主题</li>
                                <li>使用 <code>ThemeBloc</code> 进行主题切换</li>
                            </ol>
                            <p>支持动态主题切换，无需重启应用。</p>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq6">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse6">
                            如何处理网络请求？
                        </button>
                    </h2>
                    <div id="collapse6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            <p>网络层基于Dio进行封装：</p>
                            <ul>
                                <li><strong>请求拦截器</strong>: 自动添加认证头、请求日志</li>
                                <li><strong>响应拦截器</strong>: 统一错误处理、响应格式化</li>
                                <li><strong>重试机制</strong>: 自动重试失败的请求</li>
                                <li><strong>缓存支持</strong>: 支持请求结果缓存</li>
                            </ul>
                            <p>网络配置位于 <code>lib/core/network/</code> 目录。</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-5">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> 需要更多帮助？</h5>
                    <p class="mb-2">如果您遇到其他问题，可以通过以下方式获取帮助：</p>
                    <ul class="mb-0">
                        <li><strong>GitHub Issues</strong>: 提交bug报告或功能请求</li>
                        <li><strong>文档</strong>: 查看详细的API文档和开发指南</li>
                        <li><strong>社区</strong>: 加入开发者社区讨论</li>
                        <li><strong>邮件支持</strong>: <EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>
    `
};

// 显示指定部分
function showSection(sectionId) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });

    // 移除所有导航链接的active类
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 显示指定部分
    const section = document.getElementById(sectionId);
    if (section) {
        section.style.display = 'block';

        // 如果有内容数据，加载内容
        if (docContent[sectionId]) {
            section.innerHTML = docContent[sectionId];
        }

        // 添加active类到对应的导航链接
        const activeLink = document.querySelector(`a[href="#${sectionId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // 重新初始化代码高亮
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示概览部分
    showSection('overview');
});