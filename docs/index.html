<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flutter企业级应用模板 - 技术文档</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            height: 100vh;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .content {
            height: 100vh;
            overflow-y: auto;
        }
        .nav-link {
            color: #495057;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
            color: #007bff;
        }
        .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .feature-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: box-shadow 0.15s ease-in-out;
        }
        .feature-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .badge-feature {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
        .toc {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
        }
        .toc ul {
            margin-bottom: 0;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .architecture-diagram {
            text-align: center;
            margin: 2rem 0;
        }
        .layer {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.5rem;
            display: inline-block;
            min-width: 200px;
        }
        .layer.presentation { border-color: #28a745; background-color: #d4edda; }
        .layer.domain { border-color: #ffc107; background-color: #fff3cd; }
        .layer.data { border-color: #007bff; background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="px-3 mb-3">
                        <i class="fas fa-book"></i> 技术文档
                    </h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#overview" onclick="showSection('overview')">
                                <i class="fas fa-home"></i> 概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#getting-started" onclick="showSection('getting-started')">
                                <i class="fas fa-rocket"></i> 快速开始
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#architecture" onclick="showSection('architecture')">
                                <i class="fas fa-sitemap"></i> 架构设计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#features" onclick="showSection('features')">
                                <i class="fas fa-cogs"></i> 功能模块
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#api" onclick="showSection('api')">
                                <i class="fas fa-code"></i> API文档
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#development" onclick="showSection('development')">
                                <i class="fas fa-tools"></i> 开发指南
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#deployment" onclick="showSection('deployment')">
                                <i class="fas fa-cloud"></i> 部署指南
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#examples" onclick="showSection('examples')">
                                <i class="fas fa-lightbulb"></i> 示例代码
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#faq" onclick="showSection('faq')">
                                <i class="fas fa-question-circle"></i> 常见问题
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 content">
                <!-- 概览部分 -->
                <div id="overview" class="section">
                    <div class="hero-section">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-8">
                                    <h1 class="display-4">
                                        <i class="fab fa-flutter"></i>
                                        Flutter企业级应用模板
                                    </h1>
                                    <p class="lead">
                                        基于Clean Architecture的企业级Flutter应用开发模板，
                                        提供完整的架构设计、最佳实践和开发工具链。
                                    </p>
                                    <div class="mt-4">
                                        <span class="badge bg-light text-dark me-2">Flutter 3.10+</span>
                                        <span class="badge bg-light text-dark me-2">Dart 3.0+</span>
                                        <span class="badge bg-light text-dark me-2">Clean Architecture</span>
                                        <span class="badge bg-light text-dark">企业级</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="feature-card">
                                    <h5><i class="fas fa-layer-group text-primary"></i> 分层架构</h5>
                                    <p>采用Clean Architecture三层架构设计，确保代码的可维护性和可测试性。</p>
                                    <span class="badge-feature">架构设计</span>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="feature-card">
                                    <h5><i class="fas fa-puzzle-piece text-success"></i> 模块化</h5>
                                    <p>支持功能模块的动态启用/禁用，提供灵活的配置管理系统。</p>
                                    <span class="badge-feature">功能配置</span>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="feature-card">
                                    <h5><i class="fas fa-shield-alt text-warning"></i> 企业级</h5>
                                    <p>包含认证、权限、国际化、性能监控等企业级应用必需功能。</p>
                                    <span class="badge-feature">生产就绪</span>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>核心特性</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> Clean Architecture 三层架构</li>
                                            <li><i class="fas fa-check text-success"></i> BLoC 状态管理</li>
                                            <li><i class="fas fa-check text-success"></i> 依赖注入 (GetIt + Injectable)</li>
                                            <li><i class="fas fa-check text-success"></i> 声明式路由 (GoRouter)</li>
                                            <li><i class="fas fa-check text-success"></i> 多环境配置</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> 国际化支持</li>
                                            <li><i class="fas fa-check text-success"></i> 主题管理</li>
                                            <li><i class="fas fa-check text-success"></i> 网络层封装</li>
                                            <li><i class="fas fa-check text-success"></i> 数据持久化</li>
                                            <li><i class="fas fa-check text-success"></i> 完整测试框架</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他部分将通过JavaScript动态加载 -->
                <div id="getting-started" class="section" style="display: none;"></div>
                <div id="architecture" class="section" style="display: none;"></div>
                <div id="features" class="section" style="display: none;"></div>
                <div id="api" class="section" style="display: none;"></div>
                <div id="development" class="section" style="display: none;"></div>
                <div id="deployment" class="section" style="display: none;"></div>
                <div id="examples" class="section" style="display: none;"></div>
                <div id="faq" class="section" style="display: none;"></div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="js/docs.js"></script>
</body>
</html>
