import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// 核心模块导入
import 'core/di/injection.dart';
import 'core/config/feature_config.dart';
import 'core/config/environment_config.dart';
import 'core/navigation/app_router.dart';
import 'core/errors/enhanced_error_handler.dart';
import 'core/state/global_state_manager.dart';
import 'core/logging/app_logger.dart';

// 功能模块导入
import 'features/internationalization/presentation/l10n/app_localizations.dart';
import 'features/theming/presentation/bloc/theme_bloc.dart';


/// 应用程序入口点
///
/// 初始化企业级Flutter应用的基础架构
void main() async {
  AppLogger.info('🚀 应用程序启动开始');

  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  AppLogger.success('✅ Flutter绑定初始化完成');

  // 确定运行环境
  const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
  AppLogger.info('📦 运行环境: $environment');

  try {
    // 初始化依赖注入
    AppLogger.info('🔧 开始初始化依赖注入');
    await configureDependencies(environment: environment);
    AppLogger.success('✅ 依赖注入初始化完成');

    // 设置全局错误处理
    AppLogger.info('🛡️ 设置全局错误处理');
    await _setupGlobalErrorHandling();
    AppLogger.success('✅ 全局错误处理设置完成');

    // 设置系统UI样式
    AppLogger.info('🎨 设置系统UI样式');
    await _setupSystemUI();
    AppLogger.success('✅ 系统UI样式设置完成');

    // 记录功能配置状态
    final featureConfig = get<FeatureConfig>();
    AppLogger.feature('authentication', featureConfig.isFeatureEnabled('authentication'));
    AppLogger.feature('theming', featureConfig.isFeatureEnabled('theming'));
    AppLogger.feature('internationalization', featureConfig.isFeatureEnabled('internationalization'));
    AppLogger.info('⚙️ 功能配置: ${featureConfig.getEnabledFeatures()}');

    if (kDebugMode) {
      print('🚀 Flutter企业级应用启动成功');
      print('📦 环境: $environment');
      print('⚙️ 功能配置: ${featureConfig.getEnabledFeatures()}');
    }

    AppLogger.info('🎯 启动主应用');
    runApp(const FlutterEnterpriseApp());
  } catch (error, stackTrace) {
    AppLogger.error(
      '应用启动失败，启动备用版本',
      error: error,
      stackTrace: stackTrace,
    );

    if (kDebugMode) {
      print('❌ 应用启动失败: $error');
      print('📍 堆栈跟踪: $stackTrace');
    }

    // 启动简化版本应用
    AppLogger.warning('🔄 启动简化版本应用');
    runApp(const FlutterEnterpriseAppFallback());
  }
}

/// 设置全局错误处理
Future<void> _setupGlobalErrorHandling() async {
  if (getIt.isRegistered<EnhancedErrorHandler>()) {
    final errorHandler = getIt<EnhancedErrorHandler>();

    // 设置Flutter错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      errorHandler.handleFlutterError(details);
    };

    // 设置平台错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      errorHandler.handlePlatformError(error, stack);
      return true;
    };
  }
}

/// 设置系统UI样式
Future<void> _setupSystemUI() async {
  // 设置状态栏样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 设置支持的屏幕方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

/// Flutter企业级应用主类
class FlutterEnterpriseApp extends StatelessWidget {
  const FlutterEnterpriseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: _buildBlocProviders(),
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          // 只有在主题加载完成后才显示应用
          if (themeState is! ThemeLoaded) {
            return const MaterialApp(
              home: Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            );
          }

          return MaterialApp.router(
            title: 'Flutter企业级应用',

            // 主题配置
            theme: themeState.lightTheme,
            darkTheme: themeState.darkTheme,
            themeMode: themeState.themeMode,

            // 路由配置
            routerConfig: get<AppRouter>().router,

            // 国际化配置
            localizationsDelegates: _buildLocalizationsDelegates(),
            supportedLocales: _buildSupportedLocales(),

            // 调试配置
            debugShowCheckedModeBanner: false,

            // 构建器配置
            builder: (context, child) {
              return _AppWrapper(child: child);
            },
          );
        },
      ),
    );
  }

  /// 构建BLoC提供者
  List<BlocProvider> _buildBlocProviders() {
    final providers = <BlocProvider>[];

    // 主题管理
    if (get<FeatureConfig>().isFeatureEnabled('theming')) {
      providers.add(
        BlocProvider<ThemeBloc>(
          create: (context) => get<ThemeBloc>()..add(const ThemeLoadRequested()),
        ),
      );
    }

    // 其他BLoC提供者可以在这里添加

    return providers;
  }

  /// 构建本地化委托
  List<LocalizationsDelegate<dynamic>> _buildLocalizationsDelegates() {
    final delegates = <LocalizationsDelegate<dynamic>>[
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
    ];

    // 添加应用本地化委托
    if (get<FeatureConfig>().isFeatureEnabled('internationalization')) {
      delegates.add(AppLocalizations.delegate);
    }

    return delegates;
  }

  /// 构建支持的语言环境
  List<Locale> _buildSupportedLocales() {
    if (get<FeatureConfig>().isFeatureEnabled('internationalization')) {
      return const [
        Locale('zh', 'CN'), // 中文
        Locale('en', 'US'), // 英文
        Locale('ja', 'JP'), // 日文
      ];
    }

    return const [Locale('zh', 'CN')];
  }
}

/// 应用包装器
class _AppWrapper extends StatelessWidget {
  const _AppWrapper({required this.child});

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        textScaleFactor: MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.2),
      ),
      child: child ?? const SizedBox.shrink(),
    );
  }
}

/// Flutter企业级应用备用版本（当主应用启动失败时使用）
class FlutterEnterpriseAppFallback extends StatelessWidget {
  const FlutterEnterpriseAppFallback({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter企业级应用 (安全模式)',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const _FallbackHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 备用主页
class _FallbackHomePage extends StatelessWidget {
  const _FallbackHomePage();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter企业级应用 (安全模式)'),
        backgroundColor: Colors.orange,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.warning,
              color: Colors.orange,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              '应用正在安全模式下运行',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '某些高级功能可能不可用。请检查应用配置或联系技术支持。',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 24),
            Text(
              '基础功能仍然可用：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text('• 基本UI界面'),
            Text('• 本地数据存储'),
            Text('• 基础导航功能'),
          ],
        ),
      ),
    );
  }
}

