import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../domain/entities/app_theme.dart';

/// 主题事件基类
abstract class ThemeEvent extends Equatable {
  const ThemeEvent();

  @override
  List<Object?> get props => [];
}

/// 加载主题事件
class LoadThemeEvent extends ThemeEvent {
  const LoadThemeEvent();
}

/// 主题模式变更事件
class ThemeModeChangedEvent extends ThemeEvent {
  const ThemeModeChangedEvent(this.themeMode);

  final ThemeMode themeMode;

  @override
  List<Object> get props => [themeMode];
}

/// 主题变更事件
class ThemeChangedEvent extends ThemeEvent {
  const ThemeChangedEvent(this.themeId);

  final String themeId;

  @override
  List<Object> get props => [themeId];
}

/// 自定义主题保存事件
class CustomThemeSavedEvent extends ThemeEvent {
  const CustomThemeSavedEvent(this.theme);

  final AppTheme theme;

  @override
  List<Object> get props => [theme];
}

/// 自定义主题删除事件
class CustomThemeDeletedEvent extends ThemeEvent {
  const CustomThemeDeletedEvent(this.themeId);

  final String themeId;

  @override
  List<Object> get props => [themeId];
}

/// 重置主题事件
class ResetThemeEvent extends ThemeEvent {
  const ResetThemeEvent();
}
