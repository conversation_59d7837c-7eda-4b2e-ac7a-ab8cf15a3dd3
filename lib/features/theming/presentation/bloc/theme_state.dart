import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../domain/entities/app_theme.dart';

/// 主题状态基类
abstract class ThemeState extends Equatable {
  const ThemeState();

  @override
  List<Object?> get props => [];

  /// 获取亮色主题
  ThemeData get lightTheme => ThemeData.light();

  /// 获取暗色主题
  ThemeData get darkTheme => ThemeData.dark();

  /// 获取主题模式
  ThemeMode get themeMode => ThemeMode.system;

  /// 获取当前主题ID
  String get currentThemeId => 'default';

  /// 获取可用主题列表
  List<AppTheme> get availableThemes => [];
}

/// 主题初始状态
class ThemeInitial extends ThemeState {
  const ThemeInitial();
}

/// 主题加载中状态
class ThemeLoading extends ThemeState {
  const ThemeLoading();
}

/// 主题加载完成状态
class ThemeLoaded extends ThemeState {
  const ThemeLoaded({
    required this.currentTheme,
    required this.themeMode,
    required this.availableThemes,
  });

  final AppTheme currentTheme;
  @override
  final ThemeMode themeMode;
  @override
  final List<AppTheme> availableThemes;

  @override
  ThemeData get lightTheme => _buildThemeData(currentTheme, Brightness.light);

  @override
  ThemeData get darkTheme => _buildThemeData(currentTheme, Brightness.dark);

  @override
  String get currentThemeId => currentTheme.id;

  @override
  List<Object?> get props => [currentTheme, themeMode, availableThemes];

  /// 构建主题数据
  ThemeData _buildThemeData(AppTheme theme, Brightness brightness) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: theme.primaryColor,
      brightness: brightness,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: brightness,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: TextStyle(
          color: colorScheme.onInverseSurface,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 复制状态
  ThemeLoaded copyWith({
    AppTheme? currentTheme,
    ThemeMode? themeMode,
    List<AppTheme>? availableThemes,
  }) {
    return ThemeLoaded(
      currentTheme: currentTheme ?? this.currentTheme,
      themeMode: themeMode ?? this.themeMode,
      availableThemes: availableThemes ?? this.availableThemes,
    );
  }
}

/// 主题错误状态
class ThemeError extends ThemeState {
  const ThemeError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}
