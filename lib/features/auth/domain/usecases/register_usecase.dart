import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/domain/use_case_base.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../entities/auth_token.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// 注册用例参数
class RegisterParams {
  const RegisterParams({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.name,
    this.phone,
    this.acceptTerms = false,
    this.metadata = const {},
  });

  final String email;
  final String password;
  final String confirmPassword;
  final String name;
  final String? phone;
  final bool acceptTerms;
  final Map<String, dynamic> metadata;
}

/// 注册用例
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@injectable
class RegisterUseCase implements UseCaseBase<AuthResult, RegisterParams> {
  const RegisterUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Either<Failure, AuthResult> execute(RegisterParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, AuthResult>> call(RegisterParams params) async {
    // 验证输入参数
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // 创建注册信息
      final registration = UserRegistration(
        email: params.email,
        password: params.password,
        name: params.name,
        phone: params.phone,
        acceptTerms: params.acceptTerms,
        metadata: params.metadata,
      );

      // 执行注册
      final result = await _repository.register(registration);

      return result.fold(
        (failure) => Left(failure),
        (authResult) {
          // 注册成功后的额外处理
          _logRegistrationSuccess(authResult.user.email);
          return Right(authResult);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '注册过程中发生未知错误: $e'));
    }
  }

  /// 验证注册参数
  ValidationFailure? _validateParams(RegisterParams params) {
    final errors = <String, List<String>>{};

    // 验证邮箱
    if (params.email.isEmpty) {
      errors['email'] = ['邮箱地址不能为空'];
    } else if (!_isValidEmail(params.email)) {
      errors['email'] = ['请输入有效的邮箱地址'];
    }

    // 验证用户名
    if (params.name.isEmpty) {
      errors['name'] = ['用户名不能为空'];
    } else if (params.name.length < 2) {
      errors['name'] = ['用户名长度不能少于2位'];
    } else if (params.name.length > 50) {
      errors['name'] = ['用户名长度不能超过50位'];
    }

    // 验证密码
    if (params.password.isEmpty) {
      errors['password'] = ['密码不能为空'];
    } else {
      final passwordErrors = _validatePassword(params.password);
      if (passwordErrors.isNotEmpty) {
        errors['password'] = passwordErrors;
      }
    }

    // 验证确认密码
    if (params.confirmPassword.isEmpty) {
      errors['confirmPassword'] = ['确认密码不能为空'];
    } else if (params.password != params.confirmPassword) {
      errors['confirmPassword'] = ['两次输入的密码不一致'];
    }

    // 验证手机号（如果提供）
    if (params.phone != null && params.phone!.isNotEmpty) {
      if (!_isValidPhone(params.phone!)) {
        errors['phone'] = ['请输入有效的手机号码'];
      }
    }

    // 验证服务条款
    if (!params.acceptTerms) {
      errors['acceptTerms'] = ['请同意服务条款和隐私政策'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        message: '注册信息验证失败',
        details: errors,
      );
    }

    return null;
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// 验证密码强度
  List<String> _validatePassword(String password) {
    final errors = <String>[];

    if (password.length < 8) {
      errors.add('密码长度不能少于8位');
    }

    if (password.length > 128) {
      errors.add('密码长度不能超过128位');
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      errors.add('密码必须包含小写字母');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      errors.add('密码必须包含大写字母');
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      errors.add('密码必须包含数字');
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('密码必须包含特殊字符');
    }

    // 检查常见弱密码
    final commonPasswords = [
      'password',
      '12345678',
      'qwerty123',
      'abc123456',
      'password123',
    ];

    if (commonPasswords.contains(password.toLowerCase())) {
      errors.add('密码过于简单，请使用更复杂的密码');
    }

    return errors;
  }

  /// 验证手机号格式
  bool _isValidPhone(String phone) {
    // 简单的中国手机号验证
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }

  /// 记录注册成功日志
  void _logRegistrationSuccess(String email) {
    // 在实际应用中，这里可以记录到日志系统
    print('用户注册成功: $email');
  }
}

/// 忘记密码用例
@injectable
class ForgotPasswordUseCase implements UseCaseBase<void, String> {
  const ForgotPasswordUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Either<Failure, void> execute(String email) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, void>> call(String email) async {
    if (email.isEmpty) {
      return const Left(ValidationFailure(
        message: '邮箱地址不能为空',
        details: {'email': ['邮箱地址不能为空']},
      ));
    }

    if (!_isValidEmail(email)) {
      return const Left(ValidationFailure(
        message: '请输入有效的邮箱地址',
        details: {'email': ['请输入有效的邮箱地址']},
      ));
    }

    try {
      return await _repository.forgotPassword(email);
    } catch (e) {
      return Left(UnknownFailure(message: '发送重置密码邮件时发生未知错误: $e'));
    }
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }
}

/// 重置密码用例
@injectable
class ResetPasswordUseCase implements UseCaseBase<void, PasswordResetRequest> {
  const ResetPasswordUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Either<Failure, void> execute(PasswordResetRequest request) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, void>> call(PasswordResetRequest request) async {
    // 验证参数
    final validationResult = _validateRequest(request);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      return await _repository.resetPassword(request);
    } catch (e) {
      return Left(UnknownFailure(message: '重置密码时发生未知错误: $e'));
    }
  }

  /// 验证重置密码请求
  ValidationFailure? _validateRequest(PasswordResetRequest request) {
    final errors = <String, List<String>>{};

    if (request.token.isEmpty) {
      errors['token'] = ['重置令牌不能为空'];
    }

    if (request.newPassword.isEmpty) {
      errors['newPassword'] = ['新密码不能为空'];
    }

    if (request.confirmPassword.isEmpty) {
      errors['confirmPassword'] = ['确认密码不能为空'];
    }

    if (request.newPassword != request.confirmPassword) {
      errors['confirmPassword'] = ['两次输入的密码不一致'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        message: '重置密码信息验证失败',
        details: errors,
      );
    }

    return null;
  }
}
