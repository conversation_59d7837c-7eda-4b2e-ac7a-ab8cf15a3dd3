import 'package:equatable/equatable.dart';

/// 用户实体
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class User extends Equatable {
  const User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
    this.roles = const [],
    this.permissions = const [],
    this.lastLoginAt,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.metadata = const {},
  });

  /// 用户ID
  final String id;
  
  /// 邮箱地址
  final String email;
  
  /// 用户名称
  final String name;
  
  /// 头像URL
  final String? avatar;
  
  /// 手机号码
  final String? phone;
  
  /// 用户角色列表
  final List<String> roles;
  
  /// 用户权限列表
  final List<String> permissions;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;
  
  /// 创建时间
  final DateTime? createdAt;
  
  /// 更新时间
  final DateTime? updatedAt;
  
  /// 是否激活
  final bool isActive;
  
  /// 邮箱是否已验证
  final bool isEmailVerified;
  
  /// 手机是否已验证
  final bool isPhoneVerified;
  
  /// 扩展元数据
  final Map<String, dynamic> metadata;

  /// 检查用户是否有指定角色
  bool hasRole(String role) {
    return roles.contains(role);
  }

  /// 检查用户是否有指定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// 检查用户是否有任一指定角色
  bool hasAnyRole(List<String> roleList) {
    return roleList.any((role) => roles.contains(role));
  }

  /// 检查用户是否有任一指定权限
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((permission) => permissions.contains(permission));
  }

  /// 检查用户是否有所有指定角色
  bool hasAllRoles(List<String> roleList) {
    return roleList.every((role) => roles.contains(role));
  }

  /// 检查用户是否有所有指定权限
  bool hasAllPermissions(List<String> permissionList) {
    return permissionList.every((permission) => permissions.contains(permission));
  }

  /// 复制用户实体
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    String? phone,
    List<String>? roles,
    List<String>? permissions,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatar,
        phone,
        roles,
        permissions,
        lastLoginAt,
        createdAt,
        updatedAt,
        isActive,
        isEmailVerified,
        isPhoneVerified,
        metadata,
      ];

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatar': avatar,
      'phone': phone,
      'roles': roles,
      'permissions': permissions,
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isActive': isActive,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'metadata': metadata,
    };
  }

  /// 从JSON创建用户实体
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      avatar: json['avatar'] as String?,
      phone: json['phone'] as String?,
      roles: List<String>.from(json['roles'] ?? []),
      permissions: List<String>.from(json['permissions'] ?? []),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? true,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      isPhoneVerified: json['isPhoneVerified'] as bool? ?? false,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, isActive: $isActive)';
  }
}
