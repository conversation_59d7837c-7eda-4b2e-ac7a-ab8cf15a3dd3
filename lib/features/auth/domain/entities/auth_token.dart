import 'package:equatable/equatable.dart';
import 'user.dart';

/// 认证令牌实体
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class AuthToken extends Equatable {
  const AuthToken({
    required this.accessToken,
    this.refreshToken,
    this.tokenType = 'Bearer',
    this.expiresIn = 3600,
    this.scope,
    this.issuedAt,
  });

  /// 访问令牌
  final String accessToken;

  /// 刷新令牌
  final String? refreshToken;

  /// 令牌类型（通常是 "Bearer"）
  final String tokenType;

  /// 过期时间（秒）
  final int expiresIn;
  
  /// 令牌作用域
  final String? scope;
  
  /// 签发时间
  final DateTime? issuedAt;

  /// 获取过期时间
  DateTime get expiresAt {
    final issued = issuedAt ?? DateTime.now();
    return issued.add(Duration(seconds: expiresIn));
  }

  /// 检查令牌是否已过期
  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  /// 检查令牌是否即将过期（5分钟内）
  bool get isExpiringSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  /// 获取剩余有效时间
  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) {
      return Duration.zero;
    }
    return expiresAt.difference(now);
  }

  /// 复制令牌实体
  AuthToken copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    String? scope,
    DateTime? issuedAt,
  }) {
    return AuthToken(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      scope: scope ?? this.scope,
      issuedAt: issuedAt ?? this.issuedAt,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        scope,
        issuedAt,
      ];

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'scope': scope,
      'issuedAt': issuedAt?.toIso8601String(),
    };
  }

  /// 从JSON创建认证令牌
  factory AuthToken.fromJson(Map<String, dynamic> json) {
    return AuthToken(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      tokenType: json['tokenType'] as String? ?? 'Bearer',
      expiresIn: json['expiresIn'] as int? ?? 3600,
      scope: json['scope'] as String?,
      issuedAt: json['issuedAt'] != null
          ? DateTime.parse(json['issuedAt'] as String)
          : null,
    );
  }

  @override
  String toString() {
    return 'AuthToken(tokenType: $tokenType, expiresIn: $expiresIn, isExpired: $isExpired)';
  }
}


