import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/di/injection.dart';
import '../../../../features/internationalization/presentation/l10n/app_localizations.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// 注册页面
class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => get<AuthBloc>(),
      child: const _RegisterPageContent(),
    );
  }
}

class _RegisterPageContent extends StatefulWidget {
  const _RegisterPageContent();

  @override
  State<_RegisterPageContent> createState() => _RegisterPageContentState();
}

class _RegisterPageContentState extends State<_RegisterPageContent> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthAuthenticated) {
            context.go('/');
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 32),
                    _RegisterHeader(l10n: l10n),
                    const SizedBox(height: 32),
                    _NameField(
                      controller: _nameController,
                      l10n: l10n,
                    ),
                    const SizedBox(height: 16),
                    _EmailField(
                      controller: _emailController,
                      l10n: l10n,
                    ),
                    const SizedBox(height: 16),
                    _PasswordField(
                      controller: _passwordController,
                      obscurePassword: _obscurePassword,
                      onToggleVisibility: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      l10n: l10n,
                    ),
                    const SizedBox(height: 16),
                    _ConfirmPasswordField(
                      controller: _confirmPasswordController,
                      passwordController: _passwordController,
                      obscurePassword: _obscureConfirmPassword,
                      onToggleVisibility: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                      l10n: l10n,
                    ),
                    const SizedBox(height: 16),
                    _TermsCheckbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                      l10n: l10n,
                    ),
                    const SizedBox(height: 24),
                    _RegisterButton(
                      onPressed: _handleRegister,
                      enabled: _agreeToTerms,
                      l10n: l10n,
                    ),
                    const SizedBox(height: 32),
                    _LoginPrompt(l10n: l10n),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleRegister() {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_agreeToTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)?.agreeToTermsRequired ?? '请同意服务条款',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      context.read<AuthBloc>().add(
        AuthRegisterRequested(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          password: _passwordController.text,
          confirmPassword: _confirmPasswordController.text,
        ),
      );
    }
  }
}

/// 注册头部
class _RegisterHeader extends StatelessWidget {
  const _RegisterHeader({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Icon(
            Icons.person_add,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          l10n?.createAccount ?? '创建账户',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n?.registerPrompt ?? '请填写以下信息创建您的账户',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}

/// 姓名输入框
class _NameField extends StatelessWidget {
  const _NameField({
    required this.controller,
    required this.l10n,
  });

  final TextEditingController controller;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: l10n?.name ?? '姓名',
        hintText: l10n?.nameHint ?? '请输入您的姓名',
        prefixIcon: const Icon(Icons.person_outlined),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.nameRequired ?? '请输入姓名';
        }
        if (value.length < 2) {
          return l10n?.nameTooShort ?? '姓名长度至少2位';
        }
        return null;
      },
    );
  }
}

/// 邮箱输入框
class _EmailField extends StatelessWidget {
  const _EmailField({
    required this.controller,
    required this.l10n,
  });

  final TextEditingController controller;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: l10n?.email ?? '邮箱',
        hintText: l10n?.emailHint ?? '请输入邮箱地址',
        prefixIcon: const Icon(Icons.email_outlined),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.emailRequired ?? '请输入邮箱地址';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return l10n?.emailInvalid ?? '请输入有效的邮箱地址';
        }
        return null;
      },
    );
  }
}

/// 密码输入框
class _PasswordField extends StatelessWidget {
  const _PasswordField({
    required this.controller,
    required this.obscurePassword,
    required this.onToggleVisibility,
    required this.l10n,
  });

  final TextEditingController controller;
  final bool obscurePassword;
  final VoidCallback onToggleVisibility;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscurePassword,
      decoration: InputDecoration(
        labelText: l10n?.password ?? '密码',
        hintText: l10n?.passwordHint ?? '请输入密码',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: onToggleVisibility,
        ),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.passwordRequired ?? '请输入密码';
        }
        if (value.length < 8) {
          return l10n?.passwordTooShort ?? '密码长度至少8位';
        }
        if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
          return l10n?.passwordWeak ?? '密码必须包含大小写字母和数字';
        }
        return null;
      },
    );
  }
}

/// 确认密码输入框
class _ConfirmPasswordField extends StatelessWidget {
  const _ConfirmPasswordField({
    required this.controller,
    required this.passwordController,
    required this.obscurePassword,
    required this.onToggleVisibility,
    required this.l10n,
  });

  final TextEditingController controller;
  final TextEditingController passwordController;
  final bool obscurePassword;
  final VoidCallback onToggleVisibility;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscurePassword,
      decoration: InputDecoration(
        labelText: l10n?.confirmPassword ?? '确认密码',
        hintText: l10n?.confirmPasswordHint ?? '请再次输入密码',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: onToggleVisibility,
        ),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.confirmPasswordRequired ?? '请确认密码';
        }
        if (value != passwordController.text) {
          return l10n?.passwordMismatch ?? '两次输入的密码不一致';
        }
        return null;
      },
    );
  }
}

/// 服务条款复选框
class _TermsCheckbox extends StatelessWidget {
  const _TermsCheckbox({
    required this.value,
    required this.onChanged,
    required this.l10n,
  });

  final bool value;
  final ValueChanged<bool?> onChanged;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          value: value,
          onChanged: onChanged,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => onChanged(!value),
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  TextSpan(text: l10n?.agreeToTerms ?? '我同意'),
                  TextSpan(
                    text: l10n?.termsOfService ?? '服务条款',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  TextSpan(text: l10n?.and ?? '和'),
                  TextSpan(
                    text: l10n?.privacyPolicy ?? '隐私政策',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 注册按钮
class _RegisterButton extends StatelessWidget {
  const _RegisterButton({
    required this.onPressed,
    required this.enabled,
    required this.l10n,
  });

  final VoidCallback onPressed;
  final bool enabled;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return ElevatedButton(
          onPressed: (enabled && !isLoading) ? onPressed : null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(
                  l10n?.register ?? '注册',
                  style: const TextStyle(fontSize: 16),
                ),
        );
      },
    );
  }
}

/// 登录提示
class _LoginPrompt extends StatelessWidget {
  const _LoginPrompt({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          l10n?.alreadyHaveAccount ?? '已有账户？',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextButton(
          onPressed: () => context.go('/login'),
          child: Text(l10n?.login ?? '立即登录'),
        ),
      ],
    );
  }
}
