import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/di/injection.dart';
import '../../../../core/logging/app_logger.dart';
import '../../../../core/logging/page_mixin.dart';
import '../../../../features/internationalization/presentation/l10n/app_localizations.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// 登录页面
class LoginPage extends StatelessWidget with StatelessPageLoggerMixin {
  const LoginPage({super.key});

  @override
  Widget buildWithLogging(BuildContext context) {
    AppLogger.info('🔐 构建登录页面', tag: 'LoginPage');
    return BlocProvider(
      create: (context) {
        AppLogger.injection('创建AuthBloc实例', type: 'AuthBloc');
        return get<AuthBloc>();
      },
      child: const _LoginPageContent(),
    );
  }
}

class _LoginPageContent extends StatefulWidget {
  const _LoginPageContent();

  @override
  State<_LoginPageContent> createState() => _LoginPageContentState();
}

class _LoginPageContentState extends State<_LoginPageContent> with PageLoggerMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthAuthenticated) {
            context.go('/');
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _LoginHeader(l10n: l10n),
                  const SizedBox(height: 48),
                  _EmailField(
                    controller: _emailController,
                    l10n: l10n,
                  ),
                  const SizedBox(height: 16),
                  _PasswordField(
                    controller: _passwordController,
                    obscurePassword: _obscurePassword,
                    onToggleVisibility: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    l10n: l10n,
                  ),
                  const SizedBox(height: 24),
                  _LoginButton(
                    onPressed: _handleLogin,
                    l10n: l10n,
                  ),
                  const SizedBox(height: 16),
                  _ForgotPasswordButton(l10n: l10n),
                  const SizedBox(height: 32),
                  _RegisterPrompt(l10n: l10n),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
        AuthLoginRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );
    }
  }
}

/// 登录头部
class _LoginHeader extends StatelessWidget {
  const _LoginHeader({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Icon(
            Icons.flutter_dash,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          l10n?.welcomeBack ?? '欢迎回来',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n?.loginPrompt ?? '请登录您的账户',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}

/// 邮箱输入框
class _EmailField extends StatelessWidget {
  const _EmailField({
    required this.controller,
    required this.l10n,
  });

  final TextEditingController controller;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: l10n?.email ?? '邮箱',
        hintText: l10n?.emailHint ?? '请输入邮箱地址',
        prefixIcon: const Icon(Icons.email_outlined),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.emailRequired ?? '请输入邮箱地址';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return l10n?.emailInvalid ?? '请输入有效的邮箱地址';
        }
        return null;
      },
    );
  }
}

/// 密码输入框
class _PasswordField extends StatelessWidget {
  const _PasswordField({
    required this.controller,
    required this.obscurePassword,
    required this.onToggleVisibility,
    required this.l10n,
  });

  final TextEditingController controller;
  final bool obscurePassword;
  final VoidCallback onToggleVisibility;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscurePassword,
      decoration: InputDecoration(
        labelText: l10n?.password ?? '密码',
        hintText: l10n?.passwordHint ?? '请输入密码',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: onToggleVisibility,
        ),
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return l10n?.passwordRequired ?? '请输入密码';
        }
        if (value.length < 6) {
          return l10n?.passwordTooShort ?? '密码长度至少6位';
        }
        return null;
      },
    );
  }
}

/// 登录按钮
class _LoginButton extends StatelessWidget {
  const _LoginButton({
    required this.onPressed,
    required this.l10n,
  });

  final VoidCallback onPressed;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(
                  l10n?.login ?? '登录',
                  style: const TextStyle(fontSize: 16),
                ),
        );
      },
    );
  }
}

/// 忘记密码按钮
class _ForgotPasswordButton extends StatelessWidget {
  const _ForgotPasswordButton({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n?.forgotPassword ?? '忘记密码')),
        );
      },
      child: Text(l10n?.forgotPassword ?? '忘记密码？'),
    );
  }
}

/// 注册提示
class _RegisterPrompt extends StatelessWidget {
  const _RegisterPrompt({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          l10n?.noAccount ?? '还没有账户？',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextButton(
          onPressed: () => context.go('/register'),
          child: Text(l10n?.register ?? '立即注册'),
        ),
      ],
    );
  }
}
