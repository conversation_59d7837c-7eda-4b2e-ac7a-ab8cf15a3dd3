import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/use_case_base.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_token.dart';
import '../../domain/entities/auth_result.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/register_usecase.dart';

/// NoOp认证仓库实现
/// 
/// 当authentication模块禁用时使用的空实现
/// 所有认证操作返回失败状态，不执行实际认证逻辑
@Injectable(as: IAuthRepository)
@Environment('noop')
class NoOpAuthRepository implements IAuthRepository {
  const NoOpAuthRepository();

  static const _disabledFailure = AuthFailure(
    message: '认证功能未启用',
    code: 'FEATURE_DISABLED',
  );

  @override
  Future<Either<Failure, AuthResult>> login(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, AuthResult>> register(UserRegistration registration) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> logout() async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, AuthToken>> refreshToken(String refreshToken) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    return const Right(null);
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    return const Right(false);
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> resetPassword(PasswordResetRequest request) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> changePassword(PasswordChangeRequest request) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> verifyEmail(String token) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> resendEmailVerification() async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> verifyPhone(String phone, String code) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> sendPhoneVerificationCode(String phone) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> enableTwoFactorAuth(String secret, String code) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> disableTwoFactorAuth(String code) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, AuthResult>> verifyTwoFactorAuth(String code) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, String>> getTwoFactorAuthSecret() async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, AuthResult>> socialLogin(
    String provider,
    String accessToken,
  ) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> bindSocialAccount(
    String provider,
    String accessToken,
  ) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> unbindSocialAccount(String provider) async {
    return const Left(_disabledFailure);
  }

  @override
  Future<Either<Failure, void>> deleteAccount(String password) async {
    return const Left(_disabledFailure);
  }
}

/// NoOp认证用例实现
@Injectable(as: LoginUseCase)
@Environment('noop')
class NoOpLoginUseCase implements LoginUseCase {
  const NoOpLoginUseCase();

  @override
  Either<Failure, AuthResult> execute(LoginParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, AuthResult>> call(LoginParams params) async {
    return const Left(AuthFailure(
      message: '登录功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}

@Injectable(as: LogoutUseCase)
@Environment('noop')
class NoOpLogoutUseCase implements LogoutUseCase {
  const NoOpLogoutUseCase();

  @override
  Either<Failure, void> execute(NoParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    return const Left(AuthFailure(
      message: '登出功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}

@Injectable(as: RegisterUseCase)
@Environment('noop')
class NoOpRegisterUseCase implements RegisterUseCase {
  const NoOpRegisterUseCase();

  @override
  Either<Failure, AuthResult> execute(RegisterParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, AuthResult>> call(RegisterParams params) async {
    return const Left(AuthFailure(
      message: '注册功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}

@Injectable(as: GetCurrentUserUseCase)
@Environment('noop')
class NoOpGetCurrentUserUseCase implements GetCurrentUserUseCase {
  const NoOpGetCurrentUserUseCase();

  @override
  Either<Failure, User?> execute(NoParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, User?>> call(NoParams params) async {
    return const Right(null);
  }
}

@Injectable(as: CheckAuthStatusUseCase)
@Environment('noop')
class NoOpCheckAuthStatusUseCase implements CheckAuthStatusUseCase {
  const NoOpCheckAuthStatusUseCase();

  @override
  Either<Failure, bool> execute(NoParams params) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return const Right(false);
  }
}

@Injectable(as: RefreshTokenUseCase)
@Environment('noop')
class NoOpRefreshTokenUseCase implements RefreshTokenUseCase {
  const NoOpRefreshTokenUseCase();

  @override
  Either<Failure, AuthToken> execute(String refreshToken) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, AuthToken>> call(String refreshToken) async {
    return const Left(AuthFailure(
      message: '刷新令牌功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}

@Injectable(as: ForgotPasswordUseCase)
@Environment('noop')
class NoOpForgotPasswordUseCase implements ForgotPasswordUseCase {
  const NoOpForgotPasswordUseCase();

  @override
  Either<Failure, void> execute(String email) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, void>> call(String email) async {
    return const Left(AuthFailure(
      message: '忘记密码功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}

@Injectable(as: ResetPasswordUseCase)
@Environment('noop')
class NoOpResetPasswordUseCase implements ResetPasswordUseCase {
  const NoOpResetPasswordUseCase();

  @override
  Either<Failure, void> execute(PasswordResetRequest request) {
    throw UnimplementedError('Use call() method instead');
  }

  @override
  Future<Either<Failure, void>> call(PasswordResetRequest request) async {
    return const Left(AuthFailure(
      message: '重置密码功能未启用',
      code: 'FEATURE_DISABLED',
    ));
  }
}
