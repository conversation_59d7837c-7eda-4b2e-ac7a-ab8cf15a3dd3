import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';

/// 认证远程数据源接口
abstract class AuthRemoteDataSource {
  /// 用户登录
  Future<AuthResponseModel> login(LoginRequestModel request);
  
  /// 用户注册
  Future<AuthResponseModel> register(RegisterRequestModel request);
  
  /// 用户登出
  Future<void> logout();
  
  /// 刷新访问令牌
  Future<AuthTokenModel> refreshToken(String refreshToken);
  
  /// 获取当前用户信息
  Future<UserModel> getCurrentUser();
  
  /// 忘记密码
  Future<void> forgotPassword(String email);
  
  /// 重置密码
  Future<void> resetPassword(PasswordResetRequestModel request);
  
  /// 修改密码
  Future<void> changePassword(PasswordChangeRequestModel request);
  
  /// 验证邮箱
  Future<void> verifyEmail(String token);
  
  /// 重新发送邮箱验证
  Future<void> resendEmailVerification();
  
  /// 验证手机号
  Future<void> verifyPhone(String phone, String code);
  
  /// 发送手机验证码
  Future<void> sendPhoneVerificationCode(String phone);
  
  /// 启用双因素认证
  Future<void> enableTwoFactorAuth(String secret, String code);
  
  /// 禁用双因素认证
  Future<void> disableTwoFactorAuth(String code);
  
  /// 验证双因素认证码
  Future<AuthResponseModel> verifyTwoFactorAuth(String code);
  
  /// 获取双因素认证密钥
  Future<String> getTwoFactorAuthSecret();
  
  /// 社交登录
  Future<AuthResponseModel> socialLogin(String provider, String accessToken);
  
  /// 绑定社交账号
  Future<void> bindSocialAccount(String provider, String accessToken);
  
  /// 解绑社交账号
  Future<void> unbindSocialAccount(String provider);
  
  /// 删除账号
  Future<void> deleteAccount(String password);
}

/// 认证远程数据源实现
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@Injectable(as: AuthRemoteDataSource)
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<AuthResponseModel> login(LoginRequestModel request) async {
    try {
      final response = await _dio.post(
        '/auth/login',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '登录失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/login'),
        error: e,
        message: '登录请求失败: $e',
      );
    }
  }

  @override
  Future<AuthResponseModel> register(RegisterRequestModel request) async {
    try {
      final response = await _dio.post(
        '/auth/register',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '注册失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/register'),
        error: e,
        message: '注册请求失败: $e',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      final response = await _dio.post('/auth/logout');

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '登出失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/logout'),
        error: e,
        message: '登出请求失败: $e',
      );
    }
  }

  @override
  Future<AuthTokenModel> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        return AuthTokenModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '刷新令牌失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/refresh'),
        error: e,
        message: '刷新令牌请求失败: $e',
      );
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final response = await _dio.get('/auth/me');

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data['user']);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '获取用户信息失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/me'),
        error: e,
        message: '获取用户信息请求失败: $e',
      );
    }
  }

  @override
  Future<void> forgotPassword(String email) async {
    try {
      final response = await _dio.post(
        '/auth/forgot-password',
        data: {'email': email},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '发送重置密码邮件失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/forgot-password'),
        error: e,
        message: '发送重置密码邮件请求失败: $e',
      );
    }
  }

  @override
  Future<void> resetPassword(PasswordResetRequestModel request) async {
    try {
      final response = await _dio.post(
        '/auth/reset-password',
        data: request.toJson(),
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '重置密码失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/reset-password'),
        error: e,
        message: '重置密码请求失败: $e',
      );
    }
  }

  @override
  Future<void> changePassword(PasswordChangeRequestModel request) async {
    try {
      final response = await _dio.put(
        '/auth/change-password',
        data: request.toJson(),
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '修改密码失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/change-password'),
        error: e,
        message: '修改密码请求失败: $e',
      );
    }
  }

  @override
  Future<void> verifyEmail(String token) async {
    try {
      final response = await _dio.post(
        '/auth/verify-email',
        data: {'token': token},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '邮箱验证失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/verify-email'),
        error: e,
        message: '邮箱验证请求失败: $e',
      );
    }
  }

  @override
  Future<void> resendEmailVerification() async {
    try {
      final response = await _dio.post('/auth/resend-email-verification');

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '重新发送验证邮件失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/resend-email-verification'),
        error: e,
        message: '重新发送验证邮件请求失败: $e',
      );
    }
  }

  @override
  Future<void> verifyPhone(String phone, String code) async {
    try {
      final response = await _dio.post(
        '/auth/verify-phone',
        data: {'phone': phone, 'code': code},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '手机验证失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/verify-phone'),
        error: e,
        message: '手机验证请求失败: $e',
      );
    }
  }

  @override
  Future<void> sendPhoneVerificationCode(String phone) async {
    try {
      final response = await _dio.post(
        '/auth/send-phone-code',
        data: {'phone': phone},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '发送手机验证码失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/send-phone-code'),
        error: e,
        message: '发送手机验证码请求失败: $e',
      );
    }
  }

  @override
  Future<void> enableTwoFactorAuth(String secret, String code) async {
    try {
      final response = await _dio.post(
        '/auth/enable-2fa',
        data: {'secret': secret, 'code': code},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '启用双因素认证失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/enable-2fa'),
        error: e,
        message: '启用双因素认证请求失败: $e',
      );
    }
  }

  @override
  Future<void> disableTwoFactorAuth(String code) async {
    try {
      final response = await _dio.post(
        '/auth/disable-2fa',
        data: {'code': code},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '禁用双因素认证失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/disable-2fa'),
        error: e,
        message: '禁用双因素认证请求失败: $e',
      );
    }
  }

  @override
  Future<AuthResponseModel> verifyTwoFactorAuth(String code) async {
    try {
      final response = await _dio.post(
        '/auth/verify-2fa',
        data: {'code': code},
      );

      if (response.statusCode == 200) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '双因素认证验证失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/verify-2fa'),
        error: e,
        message: '双因素认证验证请求失败: $e',
      );
    }
  }

  @override
  Future<String> getTwoFactorAuthSecret() async {
    try {
      final response = await _dio.get('/auth/2fa-secret');

      if (response.statusCode == 200) {
        return response.data['secret'] as String;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '获取双因素认证密钥失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/2fa-secret'),
        error: e,
        message: '获取双因素认证密钥请求失败: $e',
      );
    }
  }

  @override
  Future<AuthResponseModel> socialLogin(String provider, String accessToken) async {
    try {
      final response = await _dio.post(
        '/auth/social-login',
        data: {'provider': provider, 'access_token': accessToken},
      );

      if (response.statusCode == 200) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '社交登录失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/social-login'),
        error: e,
        message: '社交登录请求失败: $e',
      );
    }
  }

  @override
  Future<void> bindSocialAccount(String provider, String accessToken) async {
    try {
      final response = await _dio.post(
        '/auth/bind-social',
        data: {'provider': provider, 'access_token': accessToken},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '绑定社交账号失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/bind-social'),
        error: e,
        message: '绑定社交账号请求失败: $e',
      );
    }
  }

  @override
  Future<void> unbindSocialAccount(String provider) async {
    try {
      final response = await _dio.delete('/auth/unbind-social/$provider');

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '解绑社交账号失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/unbind-social/$provider'),
        error: e,
        message: '解绑社交账号请求失败: $e',
      );
    }
  }

  @override
  Future<void> deleteAccount(String password) async {
    try {
      final response = await _dio.delete(
        '/auth/delete-account',
        data: {'password': password},
      );

      if (response.statusCode != 200) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: '删除账号失败',
        );
      }
    } on DioException {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: RequestOptions(path: '/auth/delete-account'),
        error: e,
        message: '删除账号请求失败: $e',
      );
    }
  }
}
