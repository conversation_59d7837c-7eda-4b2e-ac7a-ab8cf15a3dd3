// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthResultModel _$AuthResultModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'AuthResultModel',
      json,
      ($checkedConvert) {
        final val = AuthResultModel(
          user: $checkedConvert(
              'user', (v) => UserModel.fromJson(v as Map<String, dynamic>)),
          token: $checkedConvert('token',
              (v) => AuthTokenModel.fromJson(v as Map<String, dynamic>)),
          isFirstLogin:
              $checkedConvert('is_first_login', (v) => v as bool? ?? false),
          requiresPasswordChange: $checkedConvert(
              'requires_password_change', (v) => v as bool? ?? false),
          requiresEmailVerification: $checkedConvert(
              'requires_email_verification', (v) => v as bool? ?? false),
          requiresTwoFactorAuth: $checkedConvert(
              'requires_two_factor_auth', (v) => v as bool? ?? false),
          permissions: $checkedConvert(
              'permissions',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          roles: $checkedConvert(
              'roles',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          sessionId: $checkedConvert('session_id', (v) => v as String?),
          expiresAt: $checkedConvert('expires_at',
              (v) => v == null ? null : DateTime.parse(v as String)),
          refreshToken: $checkedConvert('refresh_token', (v) => v as String?),
          metadata: $checkedConvert(
              'metadata', (v) => v as Map<String, dynamic>? ?? const {}),
        );
        return val;
      },
      fieldKeyMap: const {
        'isFirstLogin': 'is_first_login',
        'requiresPasswordChange': 'requires_password_change',
        'requiresEmailVerification': 'requires_email_verification',
        'requiresTwoFactorAuth': 'requires_two_factor_auth',
        'sessionId': 'session_id',
        'expiresAt': 'expires_at',
        'refreshToken': 'refresh_token'
      },
    );

Map<String, dynamic> _$AuthResultModelToJson(AuthResultModel instance) =>
    <String, dynamic>{
      'user': instance.user.toJson(),
      'token': instance.token.toJson(),
      'is_first_login': instance.isFirstLogin,
      'requires_password_change': instance.requiresPasswordChange,
      'requires_email_verification': instance.requiresEmailVerification,
      'requires_two_factor_auth': instance.requiresTwoFactorAuth,
      'permissions': instance.permissions,
      'roles': instance.roles,
      if (instance.sessionId case final value?) 'session_id': value,
      if (instance.expiresAt?.toIso8601String() case final value?)
        'expires_at': value,
      if (instance.refreshToken case final value?) 'refresh_token': value,
      'metadata': instance.metadata,
    };

AuthResultResponseModel _$AuthResultResponseModelFromJson(
        Map<String, dynamic> json) =>
    $checkedCreate(
      'AuthResultResponseModel',
      json,
      ($checkedConvert) {
        final val = AuthResultResponseModel(
          success: $checkedConvert('success', (v) => v as bool),
          data: $checkedConvert(
              'data',
              (v) => v == null
                  ? null
                  : AuthResultModel.fromJson(v as Map<String, dynamic>)),
          message: $checkedConvert('message', (v) => v as String?),
          errors: $checkedConvert(
              'errors',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          metadata: $checkedConvert(
              'metadata', (v) => v as Map<String, dynamic>? ?? const {}),
        );
        return val;
      },
    );

Map<String, dynamic> _$AuthResultResponseModelToJson(
        AuthResultResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      if (instance.data?.toJson() case final value?) 'data': value,
      if (instance.message case final value?) 'message': value,
      'errors': instance.errors,
      'metadata': instance.metadata,
    };

AuthResultListResponseModel _$AuthResultListResponseModelFromJson(
        Map<String, dynamic> json) =>
    $checkedCreate(
      'AuthResultListResponseModel',
      json,
      ($checkedConvert) {
        final val = AuthResultListResponseModel(
          success: $checkedConvert('success', (v) => v as bool),
          data: $checkedConvert(
              'data',
              (v) => (v as List<dynamic>)
                  .map((e) =>
                      AuthResultModel.fromJson(e as Map<String, dynamic>))
                  .toList()),
          message: $checkedConvert('message', (v) => v as String?),
          errors: $checkedConvert(
              'errors',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          pagination:
              $checkedConvert('pagination', (v) => v as Map<String, dynamic>?),
          metadata: $checkedConvert(
              'metadata', (v) => v as Map<String, dynamic>? ?? const {}),
        );
        return val;
      },
    );

Map<String, dynamic> _$AuthResultListResponseModelToJson(
        AuthResultListResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data.map((e) => e.toJson()).toList(),
      if (instance.message case final value?) 'message': value,
      'errors': instance.errors,
      if (instance.pagination case final value?) 'pagination': value,
      'metadata': instance.metadata,
    };
