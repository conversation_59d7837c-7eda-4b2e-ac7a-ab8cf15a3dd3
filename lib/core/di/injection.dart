import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import 'package:yaml/yaml.dart';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/feature_config.dart';
import '../config/environment_config.dart';
import '../state/global_state_manager.dart';
import '../database/database_manager.dart';
import '../navigation/app_router.dart';
import '../logging/app_logger.dart';
import '../../features/theming/presentation/bloc/theme_bloc.dart';
import '../../features/theming/domain/repositories/theme_repository.dart';
import '../../features/theming/data/repositories/theme_repository_impl.dart';
import '../database/database_service.dart';
import 'injection.config.dart';

/// 全局依赖注入容器
final GetIt getIt = GetIt.instance;

/// 配置依赖注入
@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
Future<void> configureDependencies({
  String environment = 'dev',
}) async {
  AppLogger.info('🔧 开始配置依赖注入', tag: 'DI');

  // 注册环境
  AppLogger.injection('注册环境配置', type: 'String', instance: environment);
  getIt.registerSingleton<String>(
    environment,
    instanceName: 'environment',
  );

  // 初始化环境配置
  AppLogger.info('🌍 初始化环境配置: $environment', tag: 'DI');
  await EnvironmentConfig.initialize(environment);
  getIt.registerSingleton<EnvironmentConfig>(EnvironmentConfig.instance);
  AppLogger.injection('注册环境配置实例', type: 'EnvironmentConfig');

  // 初始化功能配置
  AppLogger.info('🔧 初始化功能配置', tag: 'DI');
  await _initializeFeatureConfig(environment);

  // 注册第三方依赖
  AppLogger.info('📦 注册第三方依赖', tag: 'DI');
  await _registerThirdPartyDependencies();



  // 初始化生成的依赖注入配置
  AppLogger.info('⚙️ 初始化生成的依赖注入配置', tag: 'DI');
  try {
    getIt.init(environment: environment);
    AppLogger.success('✅ 生成的依赖注入配置初始化完成', tag: 'DI');
  } catch (e) {
    AppLogger.warning('⚠️ 生成的依赖注入配置初始化失败，使用基础配置: $e', tag: 'DI');
  }

  // 注册AppRouter
  final appRouter = AppRouter(getIt<FeatureConfig>());
  getIt.registerSingleton<AppRouter>(appRouter);

  // 注册条件依赖
  await _registerConditionalDependencies();

  // 初始化核心服务
  await _initializeCoreServices();

  // 初始化AppRouter
  appRouter.initialize();

  // 验证依赖注入配置
  await _validateDependencies();
}

/// 初始化功能配置
Future<void> _initializeFeatureConfig(String environment) async {
  try {
    AppLogger.info('📋 开始加载功能配置文件', tag: 'FeatureConfig');

    // 加载功能配置
    final featuresConfigYaml = await rootBundle.loadString('assets/config/features.yaml');
    AppLogger.info('📄 YAML文件加载成功，内容长度: ${featuresConfigYaml.length}', tag: 'FeatureConfig');

    final featuresConfig = loadYaml(featuresConfigYaml) as Map;
    AppLogger.info('🔧 YAML解析成功，配置项数量: ${featuresConfig.length}', tag: 'FeatureConfig');

    // 打印前几个配置项用于调试
    final firstFewItems = featuresConfig.entries.take(5).map((e) => '${e.key}: ${e.value}').join(', ');
    AppLogger.info('📋 配置示例: $firstFewItems', tag: 'FeatureConfig');

    // 转换为FeatureConfig期望的格式
    final configMap = {
      'features': Map<String, dynamic>.from(featuresConfig),
      'dependencies': <String, dynamic>{}, // 暂时为空，后续可以扩展
    };

    AppLogger.info('🔄 配置格式转换完成', tag: 'FeatureConfig');

    // 初始化功能配置
    await FeatureConfig.initialize(configMap);

    // 注册功能配置实例
    getIt.registerSingleton<FeatureConfig>(FeatureConfig.instance);

    AppLogger.success('✅ 功能配置初始化完成', tag: 'FeatureConfig');
  } catch (e, stackTrace) {
    AppLogger.error('❌ 功能配置加载失败: $e', tag: 'FeatureConfig');
    AppLogger.error('📍 堆栈跟踪: $stackTrace', tag: 'FeatureConfig');

    // 如果配置加载失败，使用默认配置（启用基础功能）
    final defaultConfig = {
      'features': {
        'authentication': true,
        'theming': true,
        'internationalization': true,
        'dev_tools': true,
        'debug_features': true,
        'testing_tools': true,
        'mock_data': true,
        'caching': true,
        'sqlite_database': true,
      },
      'dependencies': <String, dynamic>{},
    };

    AppLogger.info('🔄 使用默认配置初始化', tag: 'FeatureConfig');
    await FeatureConfig.initialize(defaultConfig);
    getIt.registerSingleton<FeatureConfig>(FeatureConfig.instance);

    AppLogger.success('✅ 默认功能配置初始化完成', tag: 'FeatureConfig');
  }
}

/// 合并配置
void _mergeConfig(Map target, Map source) {
  source.forEach((key, value) {
    if (value is Map && target[key] is Map) {
      _mergeConfig(target[key], value);
    } else {
      target[key] = value;
    }
  });
}

/// 注册第三方依赖
Future<void> _registerThirdPartyDependencies() async {
  // Logger
  getIt.registerSingleton<Logger>(
    Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    ),
  );

  // FlutterSecureStorage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // 初始化Hive
  await Hive.initFlutter();
  getIt.registerSingleton<HiveInterface>(Hive);

  // 打开默认的Hive Box
  final box = await Hive.openBox('app_cache');
  getIt.registerSingleton<Box>(box);

  // 注册数据库服务
  getIt.registerLazySingleton<IDatabaseService>(() => HiveDatabaseService(box));

  // Connectivity
  getIt.registerSingleton<Connectivity>(Connectivity());

  // 注册重试拦截器参数
  getIt.registerSingleton<int>(3, instanceName: 'maxRetries');
  getIt.registerSingleton<Duration>(const Duration(seconds: 1), instanceName: 'retryDelay');
  getIt.registerSingleton<double>(2.0, instanceName: 'retryDelayFactor');
  getIt.registerSingleton<Duration>(const Duration(seconds: 30), instanceName: 'maxRetryDelay');

  // Dio (网络客户端)
  getIt.registerSingleton<Dio>(_createDio());
}

/// 创建Dio实例
Dio _createDio() {
  final dio = Dio();
  
  // 基础配置
  dio.options = BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  );

  return dio;
}

/// 注册条件依赖
Future<void> _registerConditionalDependencies() async {
  final featureConfig = getIt<FeatureConfig>();

  // 这里会根据功能配置注册相应的服务
  // 具体的条件依赖注册会在各个功能模块中实现

  // 注册ThemeBloc和ThemeRepository
  if (featureConfig.isFeatureEnabled('theming')) {
    // 注册ThemeRepository
    if (!isRegistered<IThemeRepository>()) {
      getIt.registerLazySingleton<IThemeRepository>(
        () => ThemeRepositoryImpl(getIt<IDatabaseService>()),
      );
    }

    // 注册ThemeBloc
    if (!isRegistered<ThemeBloc>()) {
      getIt.registerFactory<ThemeBloc>(
        () => ThemeBloc(getIt<IThemeRepository>()),
      );
    }
  } else {
    // 注册NoOp实现
    if (!isRegistered<IThemeRepository>()) {
      getIt.registerLazySingleton<IThemeRepository>(
        () => NoOpThemeRepository(),
      );
    }
  }

  // 示例：认证服务的条件注册
  // if (featureConfig.isFeatureEnabled('authentication')) {
  //   getIt.registerLazySingleton<IAuthService>(() => AuthService());
  // } else {
  //   getIt.registerLazySingleton<IAuthService>(() => NoOpAuthService());
  // }
}

/// 初始化核心服务
Future<void> _initializeCoreServices() async {
  try {
    // 初始化数据库管理器
    if (isRegistered<DatabaseManager>()) {
      final databaseManager = get<DatabaseManager>();
      await databaseManager.initialize();
    }

    // 初始化全局状态管理器
    if (isRegistered<GlobalStateManager>()) {
      final globalStateManager = get<GlobalStateManager>();
      await globalStateManager.initialize();
    }



    final logger = get<Logger>();
    logger.i('核心服务初始化完成');
  } catch (e, stackTrace) {
    final logger = get<Logger>();
    logger.e('核心服务初始化失败', error: e, stackTrace: stackTrace);
    rethrow;
  }
}

/// 验证依赖注入配置
Future<void> _validateDependencies() async {
  final logger = getIt<Logger>();
  
  try {
    // 验证核心依赖是否正确注册
    final featureConfig = getIt<FeatureConfig>();
    final secureStorage = getIt<FlutterSecureStorage>();
    final hive = getIt<HiveInterface>();
    final dio = getIt<Dio>();
    
    logger.i('依赖注入验证成功');
    logger.d('已注册的服务数量: ${getIt.allReadySync()}');
    
  } catch (e, stackTrace) {
    final logger = getIt<Logger>();
    logger.e('依赖注入验证失败', error: e, stackTrace: stackTrace);
    rethrow;
  }
}

/// 重置依赖注入容器（主要用于测试）
Future<void> resetDependencies() async {
  await getIt.reset();
}

/// 检查服务是否已注册
bool isRegistered<T extends Object>({String? instanceName}) {
  return getIt.isRegistered<T>(instanceName: instanceName);
}

/// 获取服务实例
T get<T extends Object>({String? instanceName}) {
  return getIt.get<T>(instanceName: instanceName);
}

/// 获取服务实例（异步）
Future<T> getAsync<T extends Object>({String? instanceName}) {
  return getIt.getAsync<T>(instanceName: instanceName);
}

/// 注册单例服务
void registerSingleton<T extends Object>(
  T instance, {
  String? instanceName,
  bool? signalsReady,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerSingleton<T>(
    instance,
    instanceName: instanceName,
    signalsReady: signalsReady,
    dispose: dispose,
  );
}

/// 注册懒加载单例服务
void registerLazySingleton<T extends Object>(
  FactoryFunc<T> factoryFunc, {
  String? instanceName,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerLazySingleton<T>(
    factoryFunc,
    instanceName: instanceName,
    dispose: dispose,
  );
}

/// 注册工厂服务
void registerFactory<T extends Object>(
  FactoryFunc<T> factoryFunc, {
  String? instanceName,
}) {
  getIt.registerFactory<T>(
    factoryFunc,
    instanceName: instanceName,
  );
}



/// 注销服务
Future<void> unregister<T extends Object>({
  String? instanceName,
}) async {
  await getIt.unregister<T>(
    instanceName: instanceName,
  );
}
