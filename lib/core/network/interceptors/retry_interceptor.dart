import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'dart:math';

/// 重试拦截器
/// 
/// 自动重试失败的请求，支持指数退避策略
/// **功能依赖**: 需要启用 network 模块
/// **配置项**: FEATURE_NETWORK
// @injectable  // 临时禁用以避免依赖问题
class RetryInterceptor extends Interceptor {
  RetryInterceptor({
    @Named('maxRetries') this.maxRetries = 3,
    @Named('retryDelay') this.retryDelay = const Duration(seconds: 1),
    @Named('retryDelayFactor') this.retryDelayFactor = 2.0,
    @Named('maxRetryDelay') this.maxRetryDelay = const Duration(seconds: 30),
  });

  /// 最大重试次数
  final int maxRetries;
  
  /// 基础重试延迟时间
  final Duration retryDelay;
  
  /// 重试延迟倍数因子
  final double retryDelayFactor;
  
  /// 最大重试延迟时间
  final Duration maxRetryDelay;

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    if (_shouldRetry(err)) {
      final retryCount = err.requestOptions.extra['retry_count'] ?? 0;
      
      if (retryCount < maxRetries) {
        // 计算延迟时间（指数退避）
        final delay = _calculateDelay(retryCount);
        
        // 记录重试信息
        _logRetry(err.requestOptions, retryCount + 1, delay);

        await Future.delayed(delay);

        // 更新重试次数
        err.requestOptions.extra['retry_count'] = retryCount + 1;

        try {
          // 重试请求
          final response = await Dio().fetch(err.requestOptions);
          handler.resolve(response);
          return;
        } catch (e) {
          // 重试失败，继续处理错误
          if (e is DioException) {
            handler.next(e);
          } else {
            handler.next(DioException(
              requestOptions: err.requestOptions,
              error: e,
              message: 'Retry failed: $e',
            ));
          }
          return;
        }
      }
    }

    handler.next(err);
  }

  /// 判断是否应该重试
  bool _shouldRetry(DioException err) {
    // 检查请求选项中是否禁用重试
    if (err.requestOptions.extra['disable_retry'] == true) {
      return false;
    }

    // 不重试的情况
    if (err.type == DioExceptionType.cancel) return false;
    
    // 只重试GET、HEAD、PUT、DELETE请求（幂等操作）
    final method = err.requestOptions.method.toUpperCase();
    final idempotentMethods = ['GET', 'HEAD', 'PUT', 'DELETE'];
    if (!idempotentMethods.contains(method)) {
      return false;
    }

    // 可重试的状态码
    final retryStatusCodes = [
      408, // Request Timeout
      429, // Too Many Requests
      500, // Internal Server Error
      502, // Bad Gateway
      503, // Service Unavailable
      504, // Gateway Timeout
    ];
    
    if (err.response?.statusCode != null) {
      return retryStatusCodes.contains(err.response!.statusCode);
    }

    // 网络错误可重试
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError;
  }

  /// 计算重试延迟时间
  Duration _calculateDelay(int retryCount) {
    final delayMs = retryDelay.inMilliseconds * 
                   pow(retryDelayFactor, retryCount);
    
    // 添加随机抖动（±25%）
    final jitter = Random().nextDouble() * 0.5 - 0.25; // -0.25 to 0.25
    final jitteredDelayMs = delayMs * (1 + jitter);
    
    final finalDelay = Duration(milliseconds: jitteredDelayMs.round());
    
    // 确保不超过最大延迟时间
    return finalDelay > maxRetryDelay ? maxRetryDelay : finalDelay;
  }

  /// 记录重试信息
  void _logRetry(RequestOptions options, int retryCount, Duration delay) {
    print('🔄 Retrying request: ${options.method} ${options.path}');
    print('   Retry count: $retryCount/$maxRetries');
    print('   Delay: ${delay.inMilliseconds}ms');
  }

  /// 设置请求的重试配置
  static void configureRetry(
    RequestOptions options, {
    bool? enableRetry,
    int? maxRetries,
    Duration? retryDelay,
    double? retryDelayFactor,
  }) {
    if (enableRetry == false) {
      options.extra['disable_retry'] = true;
    }
    
    if (maxRetries != null) {
      options.extra['max_retries'] = maxRetries;
    }
    
    if (retryDelay != null) {
      options.extra['retry_delay'] = retryDelay.inMilliseconds;
    }
    
    if (retryDelayFactor != null) {
      options.extra['retry_delay_factor'] = retryDelayFactor;
    }
  }

  /// 禁用特定请求的重试
  static void disableRetry(RequestOptions options) {
    options.extra['disable_retry'] = true;
  }

  /// 启用特定请求的重试
  static void enableRetry(RequestOptions options) {
    options.extra.remove('disable_retry');
  }

  /// 获取请求的重试次数
  static int getRetryCount(RequestOptions options) {
    return options.extra['retry_count'] ?? 0;
  }

  /// 重置请求的重试次数
  static void resetRetryCount(RequestOptions options) {
    options.extra.remove('retry_count');
  }
}
