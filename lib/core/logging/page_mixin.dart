import 'package:flutter/material.dart';
import 'app_logger.dart';

/// 页面生命周期日志混入
/// 
/// 为页面提供自动的生命周期日志记录
mixin PageLoggerMixin<T extends StatefulWidget> on State<T> {
  String get pageName => widget.runtimeType.toString();
  
  @override
  void initState() {
    super.initState();
    AppLogger.lifecycle(pageName, 'initState');
    onPageInit();
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    AppLogger.lifecycle(pageName, 'didChangeDependencies');
    onDependenciesChanged();
  }
  
  @override
  void didUpdateWidget(T oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.lifecycle(pageName, 'didUpdateWidget');
    onPageUpdate(oldWidget);
  }
  
  @override
  void deactivate() {
    AppLogger.lifecycle(pageName, 'deactivate');
    onPageDeactivate();
    super.deactivate();
  }
  
  @override
  void dispose() {
    AppLogger.lifecycle(pageName, 'dispose');
    onPageDispose();
    super.dispose();
  }
  
  /// 页面初始化回调
  void onPageInit() {}
  
  /// 依赖变更回调
  void onDependenciesChanged() {}
  
  /// 页面更新回调
  void onPageUpdate(T oldWidget) {}
  
  /// 页面停用回调
  void onPageDeactivate() {}
  
  /// 页面销毁回调
  void onPageDispose() {}
}

/// 无状态页面日志混入
mixin StatelessPageLoggerMixin on StatelessWidget {
  String get pageName => runtimeType.toString();
  
  @override
  Widget build(BuildContext context) {
    AppLogger.lifecycle(pageName, 'build');
    return buildWithLogging(context);
  }
  
  /// 子类需要实现的构建方法
  Widget buildWithLogging(BuildContext context);
}

/// 页面构建器 - 为任何Widget添加日志
class LoggedPageBuilder extends StatelessWidget {
  const LoggedPageBuilder({
    super.key,
    required this.pageName,
    required this.builder,
    this.onInit,
    this.onDispose,
  });
  
  final String pageName;
  final Widget Function(BuildContext context) builder;
  final VoidCallback? onInit;
  final VoidCallback? onDispose;
  
  @override
  Widget build(BuildContext context) {
    AppLogger.lifecycle(pageName, 'build');
    
    // 如果有初始化回调，执行它
    if (onInit != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppLogger.lifecycle(pageName, 'onInit callback');
        onInit!();
      });
    }
    
    return builder(context);
  }
}

/// 页面包装器 - 自动添加日志功能
Widget withPageLogging(
  String pageName,
  Widget Function(BuildContext context) builder, {
  VoidCallback? onInit,
  VoidCallback? onDispose,
}) {
  return LoggedPageBuilder(
    pageName: pageName,
    builder: builder,
    onInit: onInit,
    onDispose: onDispose,
  );
}
