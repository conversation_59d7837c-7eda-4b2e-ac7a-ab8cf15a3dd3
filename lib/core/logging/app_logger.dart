import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

/// 应用日志管理器
/// 
/// 提供统一的日志记录功能，支持不同级别的日志输出
@singleton
class AppLogger {
  static const String _tag = 'FlutterEnterpriseApp';
  
  /// 调试日志
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      final logTag = tag ?? _tag;
      developer.log(
        '🐛 $message',
        name: logTag,
        level: 500,
        error: error,
        stackTrace: stackTrace,
      );
      print('🐛 [$logTag] $message');
      if (error != null) {
        print('   Error: $error');
      }
    }
  }
  
  /// 信息日志
  static void info(String message, {String? tag, Object? data}) {
    final logTag = tag ?? _tag;
    developer.log(
      '📝 $message',
      name: logTag,
      level: 800,
    );
    print('📝 [$logTag] $message');
    if (data != null) {
      print('   Data: $data');
    }
  }
  
  /// 警告日志
  static void warning(String message, {String? tag, Object? error}) {
    final logTag = tag ?? _tag;
    developer.log(
      '⚠️ $message',
      name: logTag,
      level: 900,
      error: error,
    );
    print('⚠️ [$logTag] $message');
    if (error != null) {
      print('   Error: $error');
    }
  }
  
  /// 错误日志
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    final logTag = tag ?? _tag;
    developer.log(
      '❌ $message',
      name: logTag,
      level: 1000,
      error: error,
      stackTrace: stackTrace,
    );
    print('❌ [$logTag] $message');
    if (error != null) {
      print('   Error: $error');
    }
    if (stackTrace != null) {
      print('   StackTrace: $stackTrace');
    }
  }
  
  /// 成功日志
  static void success(String message, {String? tag, Object? data}) {
    final logTag = tag ?? _tag;
    developer.log(
      '✅ $message',
      name: logTag,
      level: 800,
    );
    print('✅ [$logTag] $message');
    if (data != null) {
      print('   Data: $data');
    }
  }
  
  /// 路由日志
  static void route(String message, {String? from, String? to, Object? params}) {
    final logTag = '${_tag}_Router';
    developer.log(
      '🧭 $message',
      name: logTag,
      level: 800,
    );
    print('🧭 [$logTag] $message');
    if (from != null) print('   From: $from');
    if (to != null) print('   To: $to');
    if (params != null) print('   Params: $params');
  }
  
  /// 页面生命周期日志
  static void lifecycle(String page, String event, {Object? data}) {
    final logTag = '${_tag}_Lifecycle';
    developer.log(
      '🔄 $page - $event',
      name: logTag,
      level: 800,
    );
    print('🔄 [$logTag] $page - $event');
    if (data != null) {
      print('   Data: $data');
    }
  }
  
  /// 依赖注入日志
  static void injection(String message, {String? type, Object? instance}) {
    final logTag = '${_tag}_DI';
    developer.log(
      '💉 $message',
      name: logTag,
      level: 800,
    );
    print('💉 [$logTag] $message');
    if (type != null) print('   Type: $type');
    if (instance != null) print('   Instance: ${instance.runtimeType}');
  }
  
  /// 功能配置日志
  static void feature(String feature, bool enabled, {String? reason}) {
    final logTag = '${_tag}_Feature';
    final status = enabled ? '✅ ENABLED' : '❌ DISABLED';
    developer.log(
      '🔧 $feature - $status',
      name: logTag,
      level: 800,
    );
    print('🔧 [$logTag] $feature - $status');
    if (reason != null) {
      print('   Reason: $reason');
    }
  }
}
