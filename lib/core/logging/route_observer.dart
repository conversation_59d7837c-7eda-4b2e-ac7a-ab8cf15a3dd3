import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'app_logger.dart';

/// 路由观察者 - 切面日志记录
class AppRouteObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    
    final routeName = route.settings.name ?? 'Unknown';
    final previousRouteName = previousRoute?.settings.name ?? 'None';
    
    AppLogger.route(
      'Page pushed',
      from: previousRouteName,
      to: routeName,
      params: route.settings.arguments,
    );
    
    AppLogger.lifecycle(routeName, 'didPush');
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    
    final routeName = route.settings.name ?? 'Unknown';
    final previousRouteName = previousRoute?.settings.name ?? 'None';
    
    AppLogger.route(
      'Page popped',
      from: routeName,
      to: previousRouteName,
    );
    
    AppLogger.lifecycle(routeName, 'didPop');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    
    final newRouteName = newRoute?.settings.name ?? 'Unknown';
    final oldRouteName = oldRoute?.settings.name ?? 'Unknown';
    
    AppLogger.route(
      'Page replaced',
      from: oldRouteName,
      to: newRouteName,
      params: newRoute?.settings.arguments,
    );
    
    AppLogger.lifecycle(newRouteName, 'didReplace');
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    
    final routeName = route.settings.name ?? 'Unknown';
    
    AppLogger.route('Page removed', from: routeName);
    AppLogger.lifecycle(routeName, 'didRemove');
  }
}

/// GoRouter 监听器
class GoRouterListener extends GoRouterDelegate {
  static void logNavigation(GoRouterState state) {
    AppLogger.route(
      'GoRouter navigation',
      to: state.matchedLocation,
      params: {
        'path': state.path,
        'fullPath': state.fullPath,
        'params': state.pathParameters,
        'query': state.uri.queryParameters,
      },
    );
  }
  
  static void logRedirect(String from, String? to) {
    if (to != null) {
      AppLogger.route('GoRouter redirect', from: from, to: to);
    } else {
      AppLogger.route('GoRouter redirect cancelled', from: from);
    }
  }
  
  static void logError(String matchedLocation, Object error) {
    AppLogger.error(
      'GoRouter error at $matchedLocation',
      tag: 'GoRouter',
      error: error,
    );
  }
}
