/// 异常定义
/// 
/// 定义了应用中使用的各种异常类型
library;

/// 基础异常类
abstract class AppException implements Exception {
  const AppException({
    required this.message,
    this.code,
    this.details,
    this.stackTrace,
  });

  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;

  @override
  String toString() => 'AppException(message: $message, code: $code)';
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.statusCode,
  });

  final int? statusCode;

  @override
  String toString() => 'NetworkException(message: $message, statusCode: $statusCode)';
}

/// 服务器异常
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.statusCode,
  });

  final int? statusCode;

  @override
  String toString() => 'ServerException(message: $message, statusCode: $statusCode)';
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'CacheException(message: $message)';
}

/// 数据库异常
class DatabaseException extends AppException {
  const DatabaseException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'DatabaseException(message: $message)';
}

/// 验证异常
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.fieldErrors,
  });

  final Map<String, List<String>>? fieldErrors;

  @override
  String toString() => 'ValidationException(message: $message, fieldErrors: $fieldErrors)';
}

/// 认证异常
class AuthenticationException extends AppException {
  const AuthenticationException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'AuthenticationException(message: $message)';
}

/// 授权异常
class AuthorizationException extends AppException {
  const AuthorizationException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'AuthorizationException(message: $message)';
}

/// 业务逻辑异常
class BusinessLogicException extends AppException {
  const BusinessLogicException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'BusinessLogicException(message: $message)';
}

/// 配置异常
class ConfigurationException extends AppException {
  const ConfigurationException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'ConfigurationException(message: $message)';
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'PermissionException(message: $message)';
}

/// 超时异常
class TimeoutException extends AppException {
  const TimeoutException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.timeout,
  });

  final Duration? timeout;

  @override
  String toString() => 'TimeoutException(message: $message, timeout: $timeout)';
}

/// 功能未启用异常
class FeatureDisabledException extends AppException {
  const FeatureDisabledException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    required this.featureName,
  });

  final String featureName;

  @override
  String toString() => 'FeatureDisabledException(message: $message, feature: $featureName)';
}

/// 依赖异常
class DependencyException extends AppException {
  const DependencyException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    required this.dependencyName,
  });

  final String dependencyName;

  @override
  String toString() => 'DependencyException(message: $message, dependency: $dependencyName)';
}

/// 并发异常
class ConcurrencyException extends AppException {
  const ConcurrencyException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'ConcurrencyException(message: $message)';
}

/// 资源不足异常
class ResourceException extends AppException {
  const ResourceException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    required this.resourceType,
  });

  final String resourceType;

  @override
  String toString() => 'ResourceException(message: $message, resource: $resourceType)';
}

/// 数据格式异常
class DataFormatException extends AppException {
  const DataFormatException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    required this.expectedFormat,
    required this.actualFormat,
  });

  final String expectedFormat;
  final String actualFormat;

  @override
  String toString() => 'DataFormatException(message: $message, expected: $expectedFormat, actual: $actualFormat)';
}

/// 版本不兼容异常
class VersionIncompatibilityException extends AppException {
  const VersionIncompatibilityException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    required this.requiredVersion,
    required this.currentVersion,
  });

  final String requiredVersion;
  final String currentVersion;

  @override
  String toString() => 'VersionIncompatibilityException(message: $message, required: $requiredVersion, current: $currentVersion)';
}

/// 数据异常
class DataException extends AppException {
  const DataException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
  });

  @override
  String toString() => 'DataException(message: $message)';
}

/// 解析异常
class ParseException extends AppException {
  const ParseException({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    this.source,
  });

  final String? source;

  @override
  String toString() => 'ParseException(message: $message, source: $source)';
}
