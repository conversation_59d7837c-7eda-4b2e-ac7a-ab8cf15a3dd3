import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:injectable/injectable.dart';

import '../config/feature_config.dart';
import '../di/injection.dart';
import '../logging/app_logger.dart';
import '../logging/route_observer.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../shared/pages/home_page.dart';
import '../../shared/pages/profile_page.dart';
import '../../shared/pages/settings_page.dart';
import '../../shared/pages/about_page.dart';
import '../../shared/pages/error_page.dart';
import '../../shared/pages/demo_pages/architecture_demo_page.dart';
import '../../shared/pages/demo_pages/feature_config_demo_page.dart';

/// 应用路由管理器
/// 
/// 基于GoRouter的声明式路由系统，支持功能配置和权限控制
@singleton
class AppRouter {
  AppRouter(this._featureConfig);

  final FeatureConfig _featureConfig;
  late final GoRouter _router;

  /// 获取路由器实例
  GoRouter get router => _router;

  /// 初始化路由配置
  @PostConstruct()
  void initialize() {
    AppLogger.info('🚀 初始化路由配置开始', tag: 'AppRouter');

    const initialLocation = '/login'; // 临时改为登录页面，方便测试
    AppLogger.route('设置初始路由', to: initialLocation);

    _router = GoRouter(
      // 🧪 测试不同页面：修改这里的路径然后热重载
      // 可用路径: '/', '/login', '/profile', '/settings', '/about', '/demo/architecture', '/demo/feature-config'
      initialLocation: initialLocation,
      observers: [AppRouteObserver()],
      errorBuilder: (context, state) {
        AppLogger.error(
          '路由错误',
          tag: 'AppRouter',
          error: state.error,
        );
        GoRouterListener.logError(state.matchedLocation, state.error ?? 'Unknown error');
        return ErrorPage(
          error: state.error?.toString() ?? '未知错误',
        );
      },
      routes: _buildRoutes(),
      redirect: _handleRedirect,
    );

    AppLogger.success('✅ 路由配置初始化完成', tag: 'AppRouter');
  }

  /// 构建路由列表
  List<RouteBase> _buildRoutes() {
    AppLogger.info('🔧 构建路由列表开始', tag: 'AppRouter');

    final routes = [
      // 主页路由
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) {
          AppLogger.route('构建首页', to: '/');
          return const HomePage();
        },
      ),

      // 认证相关路由
      if (_featureConfig.isFeatureEnabled('authentication')) ...[
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) {
            AppLogger.route('构建登录页', to: '/login');
            AppLogger.feature('authentication', true, reason: '用户访问登录页');
            return const LoginPage();
          },
        ),
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) {
            AppLogger.route('构建注册页', to: '/register');
            AppLogger.feature('authentication', true, reason: '用户访问注册页');
            return const RegisterPage();
          },
        ),
      ] else ...[
        // 如果认证功能被禁用，记录日志
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) {
            AppLogger.warning('认证功能已禁用，重定向到首页', tag: 'AppRouter');
            AppLogger.feature('authentication', false, reason: '功能被禁用');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go('/');
            });
            return const HomePage();
          },
        ),
      ],

      // 用户相关路由
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),

      // 设置相关路由
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
        routes: [
          // 主题设置
          if (_featureConfig.isFeatureEnabled('theming'))
            GoRoute(
              path: 'theme', // 移除前导斜杠
              name: 'theme_settings',
              builder: (context, state) => const ThemeSettingsPage(),
            ),
          
          // 语言设置
          if (_featureConfig.isFeatureEnabled('internationalization'))
            GoRoute(
              path: 'language', // 移除前导斜杠
              name: 'language_settings',
              builder: (context, state) => const LanguageSettingsPage(),
            ),
        ],
      ),

      // 关于页面
      GoRoute(
        path: '/about',
        name: 'about',
        builder: (context, state) => const AboutPage(),
      ),

      // 演示页面
      GoRoute(
        path: '/demo/architecture',
        name: 'architecture_demo',
        builder: (context, state) => const ArchitectureDemoPage(),
      ),
      GoRoute(
        path: '/demo/feature-config',
        name: 'feature_config_demo',
        builder: (context, state) => const FeatureConfigDemoPage(),
      ),

      // 错误页面
      GoRoute(
        path: '/error',
        name: 'error',
        builder: (context, state) => ErrorPage(
          error: state.uri.queryParameters['message'] ?? '未知错误',
        ),
      ),
    ];

    AppLogger.info('📋 路由列表构建完成，共 ${routes.length} 个路由', tag: 'AppRouter');
    return routes;
  }

  /// 处理路由重定向
  String? _handleRedirect(BuildContext context, GoRouterState state) {
    AppLogger.route('检查路由重定向', to: state.matchedLocation);

    // 如果认证功能启用，检查登录状态
    if (_featureConfig.isFeatureEnabled('authentication')) {
      AppLogger.feature('authentication', true, reason: '检查登录状态');
      // TODO: 实现登录状态检查
      // final isLoggedIn = get<IAuthService>().isLoggedIn();
      // if (!isLoggedIn && state.location != '/login' && state.location != '/register') {
      //   AppLogger.route('用户未登录，重定向到登录页', from: state.matchedLocation, to: '/login');
      //   return '/login';
      // }
    } else {
      AppLogger.feature('authentication', false, reason: '认证功能已禁用');
    }

    AppLogger.route('无需重定向', to: state.matchedLocation);
    return null; // 不重定向
  }

  /// 导航到指定路由
  void go(String location) {
    _router.go(location);
  }

  /// 推送新路由
  void push(String location) {
    _router.push(location);
  }

  /// 替换当前路由
  void replace(String location) {
    _router.replace(location);
  }

  /// 返回上一页
  void pop() {
    _router.pop();
  }

  /// 检查是否可以返回
  bool canPop() {
    return _router.canPop();
  }
}

/// 主题设置页面占位符
class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主题设置'),
      ),
      body: const Center(
        child: Text('主题设置页面'),
      ),
    );
  }
}

/// 语言设置页面占位符
class LanguageSettingsPage extends StatelessWidget {
  const LanguageSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('语言设置'),
      ),
      body: const Center(
        child: Text('语言设置页面'),
      ),
    );
  }
}
