import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:injectable/injectable.dart';

/// 数据库服务接口
/// 
/// 提供简单的键值存储功能
abstract class IDatabaseService {
  /// 初始化数据库
  Future<void> initialize();
  
  /// 关闭数据库
  Future<void> close();
  
  /// 获取值
  Future<T?> get<T>(String key);
  
  /// 设置值
  Future<void> set<T>(String key, T value);
  
  /// 删除键
  Future<void> delete(String key);
  
  /// 清空数据库
  Future<void> clear();
  
  /// 检查键是否存在
  Future<bool> exists(String key);
}

/// Hive数据库服务实现
/// 
/// 基于Hive的键值存储实现
@LazySingleton(as: IDatabaseService)
class HiveDatabaseService implements IDatabaseService {
  final Box _box;
  
  HiveDatabaseService(this._box);
  
  @override
  Future<void> initialize() async {
    // Box已经在构造函数中注入，无需额外初始化
  }
  
  @override
  Future<void> close() async {
    await _box.close();
  }
  
  @override
  Future<T?> get<T>(String key) async {
    try {
      return _box.get(key) as T?;
    } catch (e) {
      debugPrint('获取数据失败: $e');
      return null;
    }
  }
  
  @override
  Future<void> set<T>(String key, T value) async {
    try {
      await _box.put(key, value);
    } catch (e) {
      debugPrint('保存数据失败: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> delete(String key) async {
    try {
      await _box.delete(key);
    } catch (e) {
      debugPrint('删除数据失败: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> clear() async {
    try {
      await _box.clear();
    } catch (e) {
      debugPrint('清空数据失败: $e');
      rethrow;
    }
  }
  
  @override
  Future<bool> exists(String key) async {
    return _box.containsKey(key);
  }
}

/// 内存数据库服务实现
/// 
/// 基于内存的键值存储实现，用于测试
@LazySingleton(as: IDatabaseService)
@Environment('test')
class InMemoryDatabaseService implements IDatabaseService {
  final Map<String, dynamic> _storage = {};
  
  @override
  Future<void> initialize() async {
    // 内存存储无需初始化
  }
  
  @override
  Future<void> close() async {
    _storage.clear();
  }
  
  @override
  Future<T?> get<T>(String key) async {
    return _storage[key] as T?;
  }
  
  @override
  Future<void> set<T>(String key, T value) async {
    _storage[key] = value;
  }
  
  @override
  Future<void> delete(String key) async {
    _storage.remove(key);
  }
  
  @override
  Future<void> clear() async {
    _storage.clear();
  }
  
  @override
  Future<bool> exists(String key) async {
    return _storage.containsKey(key);
  }
}

/// NoOp数据库服务实现
/// 
/// 空实现，不执行任何实际操作
@LazySingleton(as: IDatabaseService)
@Environment('noop')
class NoOpDatabaseService implements IDatabaseService {
  @override
  Future<void> initialize() async {
    // NoOp: 不执行任何操作
  }
  
  @override
  Future<void> close() async {
    // NoOp: 不执行任何操作
  }
  
  @override
  Future<T?> get<T>(String key) async {
    return null;
  }
  
  @override
  Future<void> set<T>(String key, T value) async {
    // NoOp: 不执行任何操作
  }
  
  @override
  Future<void> delete(String key) async {
    // NoOp: 不执行任何操作
  }
  
  @override
  Future<void> clear() async {
    // NoOp: 不执行任何操作
  }
  
  @override
  Future<bool> exists(String key) async {
    return false;
  }
}
