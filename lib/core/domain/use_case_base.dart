import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../errors/failures.dart';

/// UseCase基础抽象类
/// 
/// 定义了用例的基本结构和执行模式
/// 每个用例代表一个具体的业务操作
abstract class UseCaseBase<Type, Params> {
  /// 执行用例
  Future<Either<Failure, Type>> call(Params params);
  
  /// 执行用例（同步版本）
  Either<Failure, Type> execute(Params params) {
    throw UnimplementedError('Synchronous execution not implemented');
  }
}

/// 无参数用例基础类
abstract class NoParamsUseCaseBase<Type> extends UseCaseBase<Type, NoParams> {
  /// 无参数执行
  Future<Either<Failure, Type>> call([NoParams? params]) async {
    return execute(params ?? NoParams());
  }

  /// 子类需要实现的执行方法
  @override
  Either<Failure, Type> execute(NoParams params) {
    throw UnimplementedError('Use call() method instead');
  }
}

/// 流式用例基础类
/// 
/// 用于处理需要持续返回数据的场景
abstract class StreamUseCaseBase<Type, Params> {
  /// 执行流式用例
  Stream<Either<Failure, Type>> call(Params params);
  
  /// 执行流式用例
  Stream<Either<Failure, Type>> execute(Params params);
}

/// 批处理用例基础类
/// 
/// 用于处理批量操作
abstract class BatchUseCaseBase<Type, Params> extends UseCaseBase<List<Type>, List<Params>> {
  /// 批量执行用例
  @override
  Future<Either<Failure, List<Type>>> call(List<Params> paramsList);
  
  /// 单个执行用例（子类可选实现）
  Future<Either<Failure, Type>> executeSingle(Params params) {
    throw UnimplementedError('Single execution not implemented');
  }
  
  /// 并行执行批处理
  Future<Either<Failure, List<Type>>> executeParallel(List<Params> paramsList) async {
    try {
      final futures = paramsList.map((params) => executeSingle(params));
      final results = await Future.wait(futures);
      
      final failures = <Failure>[];
      final successes = <Type>[];
      
      for (final result in results) {
        result.fold(
          (failure) => failures.add(failure),
          (success) => successes.add(success),
        );
      }
      
      if (failures.isNotEmpty) {
        return Left(BatchFailure(
          message: 'Batch execution failed',
          failures: failures,
        ));
      }
      
      return Right(successes);
    } catch (e) {
      return Left(UnexpectedFailure(
        message: 'Batch execution error: ${e.toString()}',
      ));
    }
  }
}

/// 可撤销用例基础类
/// 
/// 支持撤销操作的用例
abstract class UndoableUseCaseBase<Type, Params> extends UseCaseBase<Type, Params> {
  /// 撤销操作
  Future<Either<Failure, void>> undo(Params params);
  
  /// 检查是否可以撤销
  bool canUndo(Params params) => true;
}

/// 事务性用例基础类
/// 
/// 支持事务的用例
abstract class TransactionalUseCaseBase<Type, Params> extends UseCaseBase<Type, Params> {
  /// 在事务中执行
  Future<Either<Failure, Type>> executeInTransaction(Params params);
  
  /// 开始事务
  Future<void> beginTransaction();
  
  /// 提交事务
  Future<void> commitTransaction();
  
  /// 回滚事务
  Future<void> rollbackTransaction();
  
  @override
  Future<Either<Failure, Type>> call(Params params) async {
    await beginTransaction();
    try {
      final result = await executeInTransaction(params);
      if (result.isRight()) {
        await commitTransaction();
      } else {
        await rollbackTransaction();
      }
      return result;
    } catch (e) {
      await rollbackTransaction();
      return Left(TransactionFailure(
        message: 'Transaction failed: ${e.toString()}',
      ));
    }
  }
}

/// 缓存用例基础类
/// 
/// 支持缓存的用例
abstract class CacheableUseCaseBase<Type, Params> extends UseCaseBase<Type, Params> {
  /// 从缓存获取
  Future<Either<Failure, Type?>> getFromCache(Params params);
  
  /// 保存到缓存
  Future<Either<Failure, void>> saveToCache(Params params, Type result);
  
  /// 清除缓存
  Future<Either<Failure, void>> clearCache(Params params);
  
  /// 生成缓存键
  String generateCacheKey(Params params);
  
  /// 缓存过期时间
  Duration get cacheExpiration => const Duration(minutes: 30);
  
  @override
  Future<Either<Failure, Type>> call(Params params) async {
    // 先尝试从缓存获取
    final cacheResult = await getFromCache(params);
    if (cacheResult.isRight() && cacheResult.getOrElse(() => null) != null) {
      return Right(cacheResult.getOrElse(() => null)!);
    }
    
    // 缓存未命中，执行实际逻辑
    final result = await execute(params);
    
    // 保存到缓存
    if (result.isRight()) {
      await saveToCache(params, result.getOrElse(() => throw Exception()));
    }
    
    return result;
  }
}

/// 无参数类
class NoParams extends Equatable {
  @override
  List<Object?> get props => [];
}

/// 批处理失败
class BatchFailure extends Failure {
  const BatchFailure({
    required super.message,
    required this.failures,
  });
  
  final List<Failure> failures;
  
  @override
  List<Object?> get props => [...super.props, failures];
}

/// 事务失败
class TransactionFailure extends Failure {
  const TransactionFailure({
    required super.message,
  });
}

/// 意外失败
class UnexpectedFailure extends Failure {
  const UnexpectedFailure({
    required super.message,
  });
}
