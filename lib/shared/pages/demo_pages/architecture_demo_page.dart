import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/feature_config.dart';
import '../../../core/di/injection.dart';
import '../../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 架构演示页面
/// 
/// 展示Clean Architecture的三层架构实现
class ArchitectureDemoPage extends StatelessWidget {
  const ArchitectureDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.architectureTitle ?? '🏗️ 架构演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showArchitectureInfo(context, l10n),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _ArchitectureOverview(l10n: l10n),
              const SizedBox(height: 24),
              _DataLayerDemo(l10n: l10n),
              const SizedBox(height: 24),
              _DomainLayerDemo(l10n: l10n),
              const SizedBox(height: 24),
              _PresentationLayerDemo(l10n: l10n),
              const SizedBox(height: 24),
              _DependencyInjectionDemo(l10n: l10n),
            ],
          ),
        ),
      ),
    );
  }

  void _showArchitectureInfo(BuildContext context, AppLocalizations? l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n?.cleanArchitecture ?? 'Clean Architecture'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                l10n?.cleanArchitectureDesc ?? '三层架构：数据层、领域层、表现层',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('📊 数据层 (Data Layer)'),
              const Text('• 负责数据获取和存储'),
              const Text('• 包含Repository实现'),
              const Text('• 处理本地和远程数据源'),
              const SizedBox(height: 12),
              const Text('🏢 领域层 (Domain Layer)'),
              const Text('• 包含业务逻辑和规则'),
              const Text('• 定义实体和用例'),
              const Text('• 独立于外部框架'),
              const SizedBox(height: 12),
              const Text('🎨 表现层 (Presentation Layer)'),
              const Text('• 处理UI和用户交互'),
              const Text('• 使用BLoC状态管理'),
              const Text('• 响应用户操作'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n?.close ?? '关闭'),
          ),
        ],
      ),
    );
  }
}

/// 架构概览
class _ArchitectureOverview extends StatelessWidget {
  const _ArchitectureOverview({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📋 架构概览',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  _LayerItem(
                    title: '表现层 (Presentation)',
                    description: 'UI组件、BLoC、页面',
                    color: Colors.green,
                    icon: Icons.phone_android,
                  ),
                  const SizedBox(height: 8),
                  const Icon(Icons.arrow_downward, color: Colors.grey),
                  const SizedBox(height: 8),
                  _LayerItem(
                    title: '领域层 (Domain)',
                    description: '业务逻辑、实体、用例',
                    color: Colors.orange,
                    icon: Icons.business,
                  ),
                  const SizedBox(height: 8),
                  const Icon(Icons.arrow_downward, color: Colors.grey),
                  const SizedBox(height: 8),
                  _LayerItem(
                    title: '数据层 (Data)',
                    description: 'Repository、数据源、模型',
                    color: Colors.blue,
                    icon: Icons.storage,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 层级项目
class _LayerItem extends StatelessWidget {
  const _LayerItem({
    required this.title,
    required this.description,
    required this.color,
    required this.icon,
  });

  final String title;
  final String description;
  final Color color;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 数据层演示
class _DataLayerDemo extends StatelessWidget {
  const _DataLayerDemo({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 数据层演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text('数据层负责处理所有数据相关的操作：'),
            const SizedBox(height: 12),
            _DemoItem(
              icon: Icons.cloud,
              title: '远程数据源',
              description: 'API调用、网络请求',
              onTap: () => _showDataSourceDemo(context, '远程数据源'),
            ),
            _DemoItem(
              icon: Icons.storage,
              title: '本地数据源',
              description: '数据库、缓存存储',
              onTap: () => _showDataSourceDemo(context, '本地数据源'),
            ),
            _DemoItem(
              icon: Icons.transform,
              title: '数据转换',
              description: 'DTO到实体的映射',
              onTap: () => _showDataSourceDemo(context, '数据转换'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDataSourceDemo(BuildContext context, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(type),
        content: Text('这里展示$type的具体实现和使用方法。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 领域层演示
class _DomainLayerDemo extends StatelessWidget {
  const _DomainLayerDemo({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🏢 领域层演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text('领域层包含核心业务逻辑：'),
            const SizedBox(height: 12),
            _DemoItem(
              icon: Icons.account_box,
              title: '实体 (Entities)',
              description: '业务对象和规则',
              onTap: () => _showDomainDemo(context, '实体'),
            ),
            _DemoItem(
              icon: Icons.play_arrow,
              title: '用例 (Use Cases)',
              description: '业务操作和流程',
              onTap: () => _showDomainDemo(context, '用例'),
            ),
            _DemoItem(
              icon: Icons.api,
              title: '仓库接口',
              description: '数据访问抽象',
              onTap: () => _showDomainDemo(context, '仓库接口'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDomainDemo(BuildContext context, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(type),
        content: Text('这里展示$type的设计原则和实现方式。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 表现层演示
class _PresentationLayerDemo extends StatelessWidget {
  const _PresentationLayerDemo({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🎨 表现层演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text('表现层处理用户界面和交互：'),
            const SizedBox(height: 12),
            _DemoItem(
              icon: Icons.widgets,
              title: 'UI组件',
              description: '页面和小部件',
              onTap: () => _showPresentationDemo(context, 'UI组件'),
            ),
            _DemoItem(
              icon: Icons.settings_input_component,
              title: 'BLoC状态管理',
              description: '状态和事件处理',
              onTap: () => _showPresentationDemo(context, 'BLoC状态管理'),
            ),
            _DemoItem(
              icon: Icons.navigation,
              title: '路由导航',
              description: '页面跳转和参数传递',
              onTap: () => _showPresentationDemo(context, '路由导航'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPresentationDemo(BuildContext context, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(type),
        content: Text('这里展示$type的使用方法和最佳实践。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 依赖注入演示
class _DependencyInjectionDemo extends StatelessWidget {
  const _DependencyInjectionDemo({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final featureConfig = get<FeatureConfig>();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '💉 依赖注入演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text('当前注册的服务：'),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('✅ FeatureConfig: ${featureConfig.runtimeType}'),
                  Text('✅ 已启用功能: ${featureConfig.getEnabledFeatures().length}'),
                  Text('✅ 已禁用功能: ${featureConfig.getDisabledFeatures().length}'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => _showDIDetails(context),
                    child: const Text('查看详细信息'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDIDetails(BuildContext context) {
    final featureConfig = get<FeatureConfig>();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('依赖注入详情'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('已启用功能:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...featureConfig.getEnabledFeatures().map((feature) => Text('• $feature')),
              const SizedBox(height: 16),
              const Text('已禁用功能:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...featureConfig.getDisabledFeatures().map((feature) => Text('• $feature')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 演示项目
class _DemoItem extends StatelessWidget {
  const _DemoItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String description;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(description),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
