import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/feature_config.dart';
import '../../../core/di/injection.dart';
import '../../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 功能配置演示页面
/// 
/// 展示模块化功能配置的使用方法
class FeatureConfigDemoPage extends StatefulWidget {
  const FeatureConfigDemoPage({super.key});

  @override
  State<FeatureConfigDemoPage> createState() => _FeatureConfigDemoPageState();
}

class _FeatureConfigDemoPageState extends State<FeatureConfigDemoPage> {
  late FeatureConfig _featureConfig;
  late Stream<FeatureConfigChangeEvent> _configStream;

  @override
  void initState() {
    super.initState();
    _featureConfig = get<FeatureConfig>();
    _configStream = _featureConfig.onConfigChanged;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.featureConfig ?? '🔧 功能配置演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showConfigInfo(context, l10n),
          ),
        ],
      ),
      body: StreamBuilder<FeatureConfigChangeEvent>(
        stream: _configStream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _ConfigOverview(featureConfig: _featureConfig, l10n: l10n),
                  const SizedBox(height: 24),
                  _EnabledFeatures(featureConfig: _featureConfig, l10n: l10n),
                  const SizedBox(height: 24),
                  _DisabledFeatures(featureConfig: _featureConfig, l10n: l10n),
                  const SizedBox(height: 24),
                  _FeatureDependencies(featureConfig: _featureConfig, l10n: l10n),
                  const SizedBox(height: 24),
                  _DynamicConfigDemo(
                    featureConfig: _featureConfig,
                    l10n: l10n,
                    onConfigChanged: () => setState(() {}),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showConfigInfo(BuildContext context, AppLocalizations? l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n?.featureConfig ?? '功能配置'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '功能配置系统特性：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('🔧 模块化管理'),
              Text('• 支持功能的启用/禁用'),
              Text('• 运行时配置变更'),
              Text('• 依赖关系验证'),
              SizedBox(height: 12),
              Text('⚡ 动态配置'),
              Text('• 无需重启应用'),
              Text('• 实时生效'),
              Text('• 配置持久化'),
              SizedBox(height: 12),
              Text('🔒 依赖管理'),
              Text('• 自动依赖检查'),
              Text('• 循环依赖检测'),
              Text('• 安全的配置变更'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n?.close ?? '关闭'),
          ),
        ],
      ),
    );
  }
}

/// 配置概览
class _ConfigOverview extends StatelessWidget {
  const _ConfigOverview({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final enabledCount = featureConfig.getEnabledFeatures().length;
    final disabledCount = featureConfig.getDisabledFeatures().length;
    final totalCount = enabledCount + disabledCount;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 配置概览',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: '总功能数',
                    value: totalCount.toString(),
                    color: Colors.blue,
                    icon: Icons.apps,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _StatCard(
                    title: '已启用',
                    value: enabledCount.toString(),
                    color: Colors.green,
                    icon: Icons.check_circle,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _StatCard(
                    title: '已禁用',
                    value: disabledCount.toString(),
                    color: Colors.grey,
                    icon: Icons.cancel,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 统计卡片
class _StatCard extends StatelessWidget {
  const _StatCard({
    required this.title,
    required this.value,
    required this.color,
    required this.icon,
  });

  final String title;
  final String value;
  final Color color;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// 已启用功能
class _EnabledFeatures extends StatelessWidget {
  const _EnabledFeatures({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final enabledFeatures = featureConfig.getEnabledFeatures();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '✅ 已启用功能',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 16),
            if (enabledFeatures.isEmpty)
              const Text('暂无启用的功能')
            else
              ...enabledFeatures.map((feature) => _FeatureItem(
                feature: feature,
                isEnabled: true,
                dependencies: featureConfig.getFeatureDependencies(feature),
              )),
          ],
        ),
      ),
    );
  }
}

/// 已禁用功能
class _DisabledFeatures extends StatelessWidget {
  const _DisabledFeatures({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final disabledFeatures = featureConfig.getDisabledFeatures();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '❌ 已禁用功能',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            if (disabledFeatures.isEmpty)
              const Text('暂无禁用的功能')
            else
              ...disabledFeatures.map((feature) => _FeatureItem(
                feature: feature,
                isEnabled: false,
                dependencies: featureConfig.getFeatureDependencies(feature),
              )),
          ],
        ),
      ),
    );
  }
}

/// 功能项目
class _FeatureItem extends StatelessWidget {
  const _FeatureItem({
    required this.feature,
    required this.isEnabled,
    required this.dependencies,
  });

  final String feature;
  final bool isEnabled;
  final List<String> dependencies;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFeatureDisplayName(feature),
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: isEnabled ? Colors.black : Colors.grey[600],
                  ),
                ),
                if (dependencies.isNotEmpty)
                  Text(
                    '依赖: ${dependencies.join(', ')}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getFeatureDisplayName(String feature) {
    switch (feature) {
      case 'authentication':
        return '用户认证';
      case 'authorization':
        return '权限管理';
      case 'internationalization':
        return '国际化';
      case 'theming':
        return '主题管理';
      case 'analytics':
        return '数据分析';
      case 'performance_monitoring':
        return '性能监控';
      case 'push_notifications':
        return '推送通知';
      default:
        return feature;
    }
  }
}

/// 功能依赖关系
class _FeatureDependencies extends StatelessWidget {
  const _FeatureDependencies({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔗 依赖关系图',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('authorization → authentication'),
                  Text('analytics → authentication'),
                  Text('push_notifications → authentication'),
                  SizedBox(height: 8),
                  Text(
                    '说明: → 表示依赖关系',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 动态配置演示
class _DynamicConfigDemo extends StatelessWidget {
  const _DynamicConfigDemo({
    required this.featureConfig,
    required this.l10n,
    required this.onConfigChanged,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;
  final VoidCallback onConfigChanged;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '⚡ 动态配置演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text('注意：这只是演示，实际生产环境中功能配置通常在应用启动时确定。'),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showConfigDialog(context),
              icon: const Icon(Icons.settings),
              label: const Text('模拟配置变更'),
            ),
          ],
        ),
      ),
    );
  }

  void _showConfigDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('配置变更演示'),
        content: const Text(
          '在实际应用中，功能配置通常通过以下方式管理：\n\n'
          '• 配置文件 (app_config.yaml)\n'
          '• 环境变量\n'
          '• 远程配置服务\n'
          '• 构建时配置\n\n'
          '动态配置变更需要谨慎处理，确保应用状态的一致性。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
