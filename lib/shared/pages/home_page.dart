import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../core/config/feature_config.dart';
import '../../core/di/injection.dart';
import '../../core/logging/app_logger.dart';
import '../../core/logging/page_mixin.dart';
import '../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 主页
///
/// 展示应用的主要功能和状态信息
class HomePage extends StatelessWidget with StatelessPageLoggerMixin {
  const HomePage({super.key});

  @override
  Widget buildWithLogging(BuildContext context) {
    AppLogger.info('🏠 构建首页', tag: 'HomePage');
    final featureConfig = get<FeatureConfig>();
    final l10n = AppLocalizations.of(context);

    // 记录功能配置状态
    AppLogger.info('📊 当前功能配置状态: ${featureConfig.getEnabledFeatures()}', tag: 'HomePage');

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.appTitle ?? 'Flutter企业级应用'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.go('/settings'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 🧭 页面导航栏 - 方便测试各个页面
              _QuickNavigationBar(featureConfig: featureConfig),
              const SizedBox(height: 24),
              _WelcomeSection(l10n: l10n),
              const SizedBox(height: 24),
              _ArchitectureSection(l10n: l10n),
              const SizedBox(height: 24),
              _FeatureSection(featureConfig: featureConfig, l10n: l10n),
              const SizedBox(height: 24),
              _QuickActionsSection(featureConfig: featureConfig),
              const SizedBox(height: 24),
              _DemoSection(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/about'),
        child: const Icon(Icons.info),
      ),
    );
  }
}

/// 欢迎部分
class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.welcomeTitle ?? '🎉 欢迎使用Flutter企业级应用',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              l10n?.welcomeDescription ?? 
              '这是一个基于Clean Architecture的企业级Flutter应用模板，'
              '包含完整的架构设计、依赖注入、功能配置管理等企业级特性。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构部分
class _ArchitectureSection extends StatelessWidget {
  const _ArchitectureSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.architectureTitle ?? '🏗️ 架构特性',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            _ArchitectureItem(
              icon: '📁',
              title: l10n?.cleanArchitecture ?? 'Clean Architecture',
              description: l10n?.cleanArchitectureDesc ?? '三层架构：数据层、领域层、表现层',
            ),
            _ArchitectureItem(
              icon: '💉',
              title: l10n?.dependencyInjection ?? '依赖注入',
              description: l10n?.dependencyInjectionDesc ?? 'GetIt + Injectable 自动依赖注入',
            ),
            _ArchitectureItem(
              icon: '🔧',
              title: l10n?.featureConfig ?? '功能配置',
              description: l10n?.featureConfigDesc ?? '模块化功能管理，支持动态启用/禁用',
            ),
            _ArchitectureItem(
              icon: '🧪',
              title: l10n?.testFramework ?? '测试框架',
              description: l10n?.testFrameworkDesc ?? '完整的单元测试和Mock服务支持',
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构项目
class _ArchitectureItem extends StatelessWidget {
  const _ArchitectureItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  final String icon;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(icon, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 功能部分
class _FeatureSection extends StatelessWidget {
  const _FeatureSection({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.featureStatus ?? '⚡ 功能状态',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            _FeatureStatusList(featureConfig: featureConfig, l10n: l10n),
          ],
        ),
      ),
    );
  }
}

/// 功能状态列表
class _FeatureStatusList extends StatelessWidget {
  const _FeatureStatusList({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final features = [
      {
        'key': 'authentication',
        'name': l10n?.userAuth ?? '用户认证',
      },
      {
        'key': 'authorization',
        'name': l10n?.permissionManagement ?? '权限管理',
      },
      {
        'key': 'internationalization',
        'name': l10n?.internationalization ?? '国际化',
      },
      {
        'key': 'theming',
        'name': l10n?.theming ?? '主题管理',
      },
      {
        'key': 'analytics',
        'name': l10n?.analytics ?? '数据分析',
      },
    ];

    return Column(
      children: features.map((feature) {
        final isEnabled = featureConfig.isFeatureEnabled(feature['key'] as String);
        return _FeatureStatusItem(
          feature: feature['name'] as String,
          isEnabled: isEnabled,
          l10n: l10n,
        );
      }).toList(),
    );
  }
}

/// 功能状态项目
class _FeatureStatusItem extends StatelessWidget {
  const _FeatureStatusItem({
    required this.feature,
    required this.isEnabled,
    required this.l10n,
  });

  final String feature;
  final bool isEnabled;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            feature,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Spacer(),
          Text(
            isEnabled ? (l10n?.enabled ?? '已启用') : (l10n?.disabled ?? '已禁用'),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isEnabled ? Colors.green : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// 快速操作部分
class _QuickActionsSection extends StatelessWidget {
  const _QuickActionsSection({required this.featureConfig});

  final FeatureConfig featureConfig;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🚀 快速操作',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                if (featureConfig.isFeatureEnabled('authentication'))
                  ElevatedButton.icon(
                    onPressed: () => context.go('/login'),
                    icon: const Icon(Icons.login),
                    label: const Text('登录'),
                  ),
                ElevatedButton.icon(
                  onPressed: () => context.go('/profile'),
                  icon: const Icon(Icons.person),
                  label: const Text('个人资料'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.go('/settings'),
                  icon: const Icon(Icons.settings),
                  label: const Text('设置'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 演示部分
class _DemoSection extends StatelessWidget {
  const _DemoSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🎯 功能演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            const Text('探索企业级架构的各个组件和功能：'),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                ElevatedButton.icon(
                  onPressed: () => context.go('/demo/architecture'),
                  icon: const Icon(Icons.architecture),
                  label: const Text('架构演示'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.go('/demo/feature-config'),
                  icon: const Icon(Icons.settings),
                  label: const Text('功能配置'),
                ),
                OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('更多演示功能开发中...')),
                    );
                  },
                  icon: const Icon(Icons.more_horiz),
                  label: const Text('更多演示'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 快速导航栏 - 方便测试各个页面
class _QuickNavigationBar extends StatelessWidget {
  const _QuickNavigationBar({required this.featureConfig});

  final FeatureConfig featureConfig;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).colorScheme.primaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.navigation,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 8),
                Text(
                  '🧭 页面导航 - 点击测试各个页面',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                // 认证页面
                if (featureConfig.isFeatureEnabled('authentication')) ...[
                  _NavButton(
                    icon: Icons.login,
                    label: '登录',
                    onPressed: () => context.go('/login'),
                  ),
                  _NavButton(
                    icon: Icons.person_add,
                    label: '注册',
                    onPressed: () => context.go('/register'),
                  ),
                ],
                // 主要页面
                _NavButton(
                  icon: Icons.person,
                  label: '个人资料',
                  onPressed: () => context.go('/profile'),
                ),
                _NavButton(
                  icon: Icons.settings,
                  label: '设置',
                  onPressed: () => context.go('/settings'),
                ),
                _NavButton(
                  icon: Icons.info,
                  label: '关于',
                  onPressed: () => context.go('/about'),
                ),
                // 演示页面
                _NavButton(
                  icon: Icons.architecture,
                  label: '架构演示',
                  onPressed: () => context.go('/demo/architecture'),
                ),
                _NavButton(
                  icon: Icons.tune,
                  label: '功能配置',
                  onPressed: () => context.go('/demo/feature-config'),
                ),
                // 设置子页面
                if (featureConfig.isFeatureEnabled('theming'))
                  _NavButton(
                    icon: Icons.palette,
                    label: '主题设置',
                    onPressed: () => context.go('/settings/theme'),
                  ),
                if (featureConfig.isFeatureEnabled('internationalization'))
                  _NavButton(
                    icon: Icons.language,
                    label: '语言设置',
                    onPressed: () => context.go('/settings/language'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 导航按钮
class _NavButton extends StatelessWidget {
  const _NavButton({
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  final IconData icon;
  final String label;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: const Size(0, 32),
      ),
    );
  }
}
