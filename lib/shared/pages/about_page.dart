import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/config/feature_config.dart';
import '../../core/di/injection.dart';
import '../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 关于页面
class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    final featureConfig = get<FeatureConfig>();
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.about ?? '关于'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _AppInfoSection(l10n: l10n),
              const SizedBox(height: 24),
              _ArchitectureInfoSection(l10n: l10n),
              const SizedBox(height: 24),
              _FeaturesInfoSection(featureConfig: featureConfig, l10n: l10n),
              const SizedBox(height: 24),
              _TechnicalInfoSection(l10n: l10n),
              const SizedBox(height: 24),
              _ContactInfoSection(l10n: l10n),
            ],
          ),
        ),
      ),
    );
  }
}

/// 应用信息部分
class _AppInfoSection extends StatelessWidget {
  const _AppInfoSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.flutter_dash,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              l10n?.appTitle ?? 'Flutter企业级应用',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              l10n?.version ?? '版本 1.0.0',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              l10n?.appDescription ?? 
              '基于Clean Architecture的企业级Flutter应用模板，'
              '提供完整的架构设计和最佳实践。',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构信息部分
class _ArchitectureInfoSection extends StatelessWidget {
  const _ArchitectureInfoSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.architectureTitle ?? '🏗️ 架构特性',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _InfoItem(
              title: l10n?.cleanArchitecture ?? 'Clean Architecture',
              description: l10n?.cleanArchitectureDesc ?? '三层架构：数据层、领域层、表现层',
            ),
            _InfoItem(
              title: l10n?.dependencyInjection ?? '依赖注入',
              description: l10n?.dependencyInjectionDesc ?? 'GetIt + Injectable 自动依赖注入',
            ),
            _InfoItem(
              title: l10n?.stateManagement ?? '状态管理',
              description: l10n?.stateManagementDesc ?? 'BLoC模式状态管理',
            ),
            _InfoItem(
              title: l10n?.routing ?? '路由管理',
              description: l10n?.routingDesc ?? 'GoRouter声明式路由',
            ),
          ],
        ),
      ),
    );
  }
}

/// 功能信息部分
class _FeaturesInfoSection extends StatelessWidget {
  const _FeaturesInfoSection({
    required this.featureConfig,
    required this.l10n,
  });

  final FeatureConfig featureConfig;
  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    final enabledFeatures = featureConfig.getEnabledFeatures();
    final disabledFeatures = featureConfig.getDisabledFeatures();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.featureStatus ?? '⚡ 功能状态',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Text(
              l10n?.enabledFeatures ?? '已启用功能:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...enabledFeatures.map((feature) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2.0),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Text(_getFeatureDisplayName(feature, l10n)),
                ],
              ),
            )),
            if (disabledFeatures.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                l10n?.disabledFeatures ?? '已禁用功能:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...disabledFeatures.map((feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  children: [
                    const Icon(Icons.cancel, color: Colors.grey, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      _getFeatureDisplayName(feature, l10n),
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  String _getFeatureDisplayName(String feature, AppLocalizations? l10n) {
    switch (feature) {
      case 'authentication':
        return l10n?.userAuth ?? '用户认证';
      case 'authorization':
        return l10n?.permissionManagement ?? '权限管理';
      case 'internationalization':
        return l10n?.internationalization ?? '国际化';
      case 'theming':
        return l10n?.theming ?? '主题管理';
      case 'analytics':
        return l10n?.analytics ?? '数据分析';
      case 'performance_monitoring':
        return l10n?.performanceMonitoring ?? '性能监控';
      case 'push_notifications':
        return l10n?.pushNotifications ?? '推送通知';
      default:
        return feature;
    }
  }
}

/// 技术信息部分
class _TechnicalInfoSection extends StatelessWidget {
  const _TechnicalInfoSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.technicalInfo ?? '🔧 技术信息',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _InfoItem(
              title: 'Flutter SDK',
              description: '3.10.0+',
            ),
            _InfoItem(
              title: 'Dart SDK',
              description: '3.0.0+',
            ),
            _InfoItem(
              title: l10n?.buildDate ?? '构建日期',
              description: '2024-01-01',
            ),
            _InfoItem(
              title: l10n?.environment ?? '运行环境',
              description: 'Development',
            ),
          ],
        ),
      ),
    );
  }
}

/// 联系信息部分
class _ContactInfoSection extends StatelessWidget {
  const _ContactInfoSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.contactInfo ?? '📞 联系信息',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.email),
              title: Text(l10n?.email ?? '邮箱'),
              subtitle: const Text('<EMAIL>'),
              onTap: () => _copyToClipboard(context, '<EMAIL>', l10n),
            ),
            ListTile(
              leading: const Icon(Icons.web),
              title: Text(l10n?.website ?? '官网'),
              subtitle: const Text('https://example.com'),
              onTap: () => _copyToClipboard(context, 'https://example.com', l10n),
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: Text(l10n?.sourceCode ?? '源代码'),
              subtitle: const Text('https://github.com/example/flutter-template'),
              onTap: () => _copyToClipboard(context, 'https://github.com/example/flutter-template', l10n),
            ),
          ],
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text, AppLocalizations? l10n) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(l10n?.copiedToClipboard ?? '已复制到剪贴板'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// 信息项目
class _InfoItem extends StatelessWidget {
  const _InfoItem({
    required this.title,
    required this.description,
  });

  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
