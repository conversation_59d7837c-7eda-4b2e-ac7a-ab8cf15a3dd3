import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../core/config/feature_config.dart';
import '../../core/di/injection.dart';
import '../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 个人资料页面
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final featureConfig = get<FeatureConfig>();
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.profile ?? '个人资料'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: 实现编辑功能
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n?.editProfile ?? '编辑个人资料')),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _ProfileHeader(l10n: l10n),
            const SizedBox(height: 24),
            _ProfileInfo(l10n: l10n),
            const SizedBox(height: 24),
            if (featureConfig.isFeatureEnabled('authentication'))
              _AuthSection(l10n: l10n),
          ],
        ),
      ),
    );
  }
}

/// 个人资料头部
class _ProfileHeader extends StatelessWidget {
  const _ProfileHeader({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(
                Icons.person,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n?.userName ?? '演示用户',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '<EMAIL>',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      l10n?.active ?? '活跃',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 个人信息
class _ProfileInfo extends StatelessWidget {
  const _ProfileInfo({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.personalInfo ?? '个人信息',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _InfoItem(
              icon: Icons.phone,
              label: l10n?.phone ?? '手机号码',
              value: '+86 138 0013 8000',
            ),
            _InfoItem(
              icon: Icons.location_on,
              label: l10n?.location ?? '所在地',
              value: l10n?.locationValue ?? '北京市，中国',
            ),
            _InfoItem(
              icon: Icons.work,
              label: l10n?.occupation ?? '职业',
              value: l10n?.occupationValue ?? '软件开发工程师',
            ),
            _InfoItem(
              icon: Icons.calendar_today,
              label: l10n?.joinDate ?? '加入时间',
              value: '2024-01-01',
            ),
          ],
        ),
      ),
    );
  }
}

/// 信息项目
class _InfoItem extends StatelessWidget {
  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
  });

  final IconData icon;
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 认证部分
class _AuthSection extends StatelessWidget {
  const _AuthSection({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.accountSecurity ?? '账户安全',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.lock),
              title: Text(l10n?.changePassword ?? '修改密码'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(l10n?.changePassword ?? '修改密码')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: Text(l10n?.twoFactorAuth ?? '双因素认证'),
              trailing: Switch(
                value: false,
                onChanged: (value) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n?.twoFactorAuth ?? '双因素认证')),
                  );
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: Text(l10n?.logout ?? '退出登录'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showLogoutDialog(context, l10n);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AppLocalizations? l10n) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n?.logout ?? '退出登录'),
          content: Text(l10n?.logoutConfirm ?? '确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go('/login');
              },
              child: Text(l10n?.confirm ?? '确定'),
            ),
          ],
        );
      },
    );
  }
}
