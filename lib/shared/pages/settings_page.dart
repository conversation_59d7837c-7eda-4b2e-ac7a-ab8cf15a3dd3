import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../core/config/feature_config.dart';
import '../../core/di/injection.dart';
import '../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 设置页面
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final featureConfig = get<FeatureConfig>();
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.settings ?? '设置'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _SettingsSection(
            title: l10n?.appearance ?? '外观设置',
            children: [
              if (featureConfig.isFeatureEnabled('theming'))
                _SettingsItem(
                  icon: Icons.palette,
                  title: l10n?.theme ?? '主题',
                  subtitle: l10n?.themeDesc ?? '选择应用主题',
                  onTap: () => context.go('/settings/theme'),
                ),
              if (featureConfig.isFeatureEnabled('internationalization'))
                _SettingsItem(
                  icon: Icons.language,
                  title: l10n?.language ?? '语言',
                  subtitle: l10n?.languageDesc ?? '选择应用语言',
                  onTap: () => context.go('/settings/language'),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _SettingsSection(
            title: l10n?.account ?? '账户设置',
            children: [
              _SettingsItem(
                icon: Icons.person,
                title: l10n?.profile ?? '个人资料',
                subtitle: l10n?.profileDesc ?? '管理个人信息',
                onTap: () => context.go('/profile'),
              ),
              if (featureConfig.isFeatureEnabled('authentication'))
                _SettingsItem(
                  icon: Icons.security,
                  title: l10n?.security ?? '安全设置',
                  subtitle: l10n?.securityDesc ?? '密码和安全选项',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(l10n?.security ?? '安全设置')),
                    );
                  },
                ),
            ],
          ),
          const SizedBox(height: 16),
          _SettingsSection(
            title: l10n?.general ?? '通用设置',
            children: [
              _SettingsItem(
                icon: Icons.notifications,
                title: l10n?.notifications ?? '通知',
                subtitle: l10n?.notificationsDesc ?? '管理通知设置',
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n?.notifications ?? '通知')),
                  );
                },
              ),
              _SettingsItem(
                icon: Icons.storage,
                title: l10n?.storage ?? '存储',
                subtitle: l10n?.storageDesc ?? '管理应用数据',
                onTap: () {
                  _showStorageDialog(context, l10n);
                },
              ),
              _SettingsItem(
                icon: Icons.privacy_tip,
                title: l10n?.privacy ?? '隐私',
                subtitle: l10n?.privacyDesc ?? '隐私和数据保护',
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n?.privacy ?? '隐私')),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          _SettingsSection(
            title: l10n?.about ?? '关于',
            children: [
              _SettingsItem(
                icon: Icons.info,
                title: l10n?.aboutApp ?? '关于应用',
                subtitle: l10n?.aboutAppDesc ?? '版本信息和帮助',
                onTap: () => context.go('/about'),
              ),
              _SettingsItem(
                icon: Icons.help,
                title: l10n?.help ?? '帮助',
                subtitle: l10n?.helpDesc ?? '使用帮助和支持',
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n?.help ?? '帮助')),
                  );
                },
              ),
              _SettingsItem(
                icon: Icons.feedback,
                title: l10n?.feedback ?? '反馈',
                subtitle: l10n?.feedbackDesc ?? '意见和建议',
                onTap: () {
                  _showFeedbackDialog(context, l10n);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showStorageDialog(BuildContext context, AppLocalizations? l10n) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n?.storage ?? '存储'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(l10n?.storageInfo ?? '存储使用情况：'),
              const SizedBox(height: 8),
              const Text('• 缓存数据: 12.5 MB'),
              const Text('• 用户数据: 3.2 MB'),
              const Text('• 临时文件: 1.8 MB'),
              const SizedBox(height: 16),
              Text(
                l10n?.totalStorage ?? '总计: 17.5 MB',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n?.close ?? '关闭'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(l10n?.clearCache ?? '清除缓存')),
                );
              },
              child: Text(l10n?.clearCache ?? '清除缓存'),
            ),
          ],
        );
      },
    );
  }

  void _showFeedbackDialog(BuildContext context, AppLocalizations? l10n) {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n?.feedback ?? '反馈'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(l10n?.feedbackPrompt ?? '请输入您的意见和建议：'),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: l10n?.feedbackHint ?? '请输入反馈内容...',
                  border: const OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(l10n?.feedbackSubmitted ?? '反馈已提交')),
                );
              },
              child: Text(l10n?.submit ?? '提交'),
            ),
          ],
        );
      },
    );
  }
}

/// 设置分组
class _SettingsSection extends StatelessWidget {
  const _SettingsSection({
    required this.title,
    required this.children,
  });

  final String title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...children,
          ],
        ),
      ),
    );
  }
}

/// 设置项目
class _SettingsItem extends StatelessWidget {
  const _SettingsItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
