import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/internationalization/presentation/l10n/app_localizations.dart';

/// 错误页面
class ErrorPage extends StatelessWidget {
  const ErrorPage({
    super.key,
    required this.error,
    this.stackTrace,
  });

  final String error;
  final String? stackTrace;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.error ?? '错误'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _ErrorHeader(l10n: l10n),
            const SizedBox(height: 24),
            _ErrorDetails(error: error, stackTrace: stackTrace, l10n: l10n),
            const SizedBox(height: 24),
            _ErrorActions(l10n: l10n),
          ],
        ),
      ),
    );
  }
}

/// 错误头部
class _ErrorHeader extends StatelessWidget {
  const _ErrorHeader({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.shade700,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n?.errorOccurred ?? '发生错误',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    l10n?.errorDescription ?? '应用遇到了一个意外错误，我们正在努力解决这个问题。',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 错误详情
class _ErrorDetails extends StatefulWidget {
  const _ErrorDetails({
    required this.error,
    required this.stackTrace,
    required this.l10n,
  });

  final String error;
  final String? stackTrace;
  final AppLocalizations? l10n;

  @override
  State<_ErrorDetails> createState() => _ErrorDetailsState();
}

class _ErrorDetailsState extends State<_ErrorDetails> {
  bool _showDetails = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.l10n?.errorDetails ?? '错误详情',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showDetails = !_showDetails;
                    });
                  },
                  child: Text(
                    _showDetails 
                        ? (widget.l10n?.hide ?? '隐藏')
                        : (widget.l10n?.show ?? '显示'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                widget.error,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
            if (_showDetails && widget.stackTrace != null) ...[
              const SizedBox(height: 16),
              Text(
                widget.l10n?.stackTrace ?? '堆栈跟踪',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 200,
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    widget.stackTrace!,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 错误操作
class _ErrorActions extends StatelessWidget {
  const _ErrorActions({required this.l10n});

  final AppLocalizations? l10n;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n?.suggestedActions ?? '建议操作',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => context.go('/'),
                    icon: const Icon(Icons.home),
                    label: Text(l10n?.backToHome ?? '返回首页'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // 重新加载当前页面
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.refresh),
                    label: Text(l10n?.retry ?? '重试'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  _showReportDialog(context);
                },
                icon: const Icon(Icons.bug_report),
                label: Text(l10n?.reportError ?? '报告错误'),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              l10n?.errorTips ?? '提示：',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              l10n?.errorTipsContent ?? 
              '• 检查网络连接是否正常\n'
              '• 尝试重启应用\n'
              '• 如果问题持续存在，请联系技术支持',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _showReportDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n?.reportError ?? '报告错误'),
          content: Text(
            l10n?.reportErrorContent ?? 
            '感谢您报告这个错误。错误信息将被发送给开发团队进行分析和修复。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(l10n?.errorReported ?? '错误已报告'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: Text(l10n?.send ?? '发送'),
            ),
          ],
        );
      },
    );
  }
}
