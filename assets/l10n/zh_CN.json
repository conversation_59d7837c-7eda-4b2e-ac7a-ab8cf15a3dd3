{"appTitle": "Flutter企业级应用", "welcomeTitle": "🎉 欢迎使用Flutter企业级应用", "welcomeDescription": "这是一个基于Clean Architecture的企业级Flutter应用模板，包含完整的架构设计、依赖注入、功能配置管理等企业级特性。", "architectureTitle": "🏗️ 架构演示", "cleanArchitecture": "Clean Architecture", "cleanArchitectureDesc": "三层架构：数据层、领域层、表现层", "dependencyInjection": "依赖注入", "dependencyInjectionDesc": "GetIt + Injectable 自动依赖注入", "featureConfig": "功能配置", "featureConfigDesc": "模块化功能管理，支持动态启用/禁用", "testFramework": "测试框架", "testFrameworkDesc": "完整的单元测试和Mock服务支持", "stateManagement": "状态管理", "stateManagementDesc": "BLoC模式状态管理", "routing": "路由管理", "routingDesc": "Go Router声明式路由", "featureStatus": "⚡ 功能状态", "userAuth": "用户认证", "permissionManagement": "权限管理", "internationalization": "国际化", "theming": "主题管理", "analytics": "数据分析", "performanceMonitoring": "性能监控", "pushNotifications": "推送通知", "enabled": "已启用", "disabled": "已禁用", "enabledFeatures": "已启用功能", "disabledFeatures": "已禁用功能", "profile": "个人资料", "settings": "设置", "about": "关于", "error": "错误", "userName": "用户名", "active": "活跃", "personalInfo": "个人信息", "phone": "手机号码", "location": "所在地", "locationValue": "北京市朝阳区", "occupation": "职业", "occupationValue": "软件工程师", "joinDate": "加入日期", "accountSecurity": "账户安全", "changePassword": "修改密码", "twoFactorAuth": "双因素认证", "logout": "退出登录", "logoutConfirm": "确认退出登录？", "cancel": "取消", "confirm": "确定", "appearance": "外观设置", "theme": "主题", "themeDesc": "选择您喜欢的主题", "language": "语言", "languageDesc": "选择您的语言", "account": "账户设置", "profileDesc": "管理您的个人资料", "security": "安全设置", "securityDesc": "账户安全设置", "general": "通用设置", "notifications": "通知", "notificationsDesc": "通知设置", "storage": "存储", "storageDesc": "存储管理", "storageInfo": "存储信息", "totalStorage": "总存储空间", "clearCache": "清除缓存", "close": "关闭", "privacy": "隐私", "privacyDesc": "隐私设置", "aboutApp": "关于应用", "aboutAppDesc": "关于应用", "help": "帮助", "helpDesc": "帮助与支持", "feedback": "反馈", "feedbackDesc": "意见反馈", "feedbackPrompt": "请告诉我们您的意见和建议", "feedbackHint": "请输入您的反馈内容...", "feedbackSubmitted": "反馈已提交", "submit": "提交", "version": "版本 1.0.0", "appDescription": "基于Clean Architecture的企业级Flutter应用模板", "technicalInfo": "技术信息", "buildDate": "构建日期", "environment": "运行环境", "contactInfo": "📞 联系信息", "email": "邮箱", "website": "官网", "sourceCode": "源代码", "copiedToClipboard": "已复制到剪贴板", "errorOccurred": "发生错误", "errorDescription": "应用遇到了一个意外错误，我们正在努力解决这个问题。", "errorDetails": "错误详情", "hide": "隐藏", "show": "显示", "stackTrace": "堆栈跟踪", "suggestedActions": "建议操作", "backToHome": "返回首页", "retry": "重试", "reportError": "报告错误", "errorTips": "提示：", "errorTipsContent": "• 检查网络连接是否正常\n• 尝试重启应用\n• 如果问题持续存在，请联系技术支持", "reportErrorContent": "感谢您报告这个错误。错误信息将被发送给开发团队进行分析和修复。", "errorReported": "错误已报告", "send": "发送", "welcomeBack": "欢迎回来", "loginPrompt": "请登录您的账户", "emailHint": "请输入邮箱地址", "emailRequired": "请输入邮箱", "emailInvalid": "邮箱格式不正确", "password": "密码", "passwordHint": "请输入密码", "passwordRequired": "请输入密码", "passwordTooShort": "密码长度至少6位", "login": "登录", "forgotPassword": "忘记密码？", "noAccount": "还没有账户？", "register": "立即注册", "createAccount": "创建账户", "registerPrompt": "创建您的新账户", "name": "姓名", "nameHint": "请输入您的姓名", "nameRequired": "请输入姓名", "nameTooShort": "姓名长度至少2位", "passwordWeak": "密码必须包含大小写字母和数字", "confirmPassword": "确认密码", "confirmPasswordHint": "请再次输入密码", "confirmPasswordRequired": "请确认密码", "passwordMismatch": "两次输入的密码不一致", "agreeToTerms": "我同意", "termsOfService": "服务条款", "and": "和", "privacyPolicy": "隐私政策", "agreeToTermsRequired": "请同意服务条款", "alreadyHaveAccount": "已有账户？", "editProfile": "编辑个人资料", "error_general": "发生错误，请重试。", "error_network": "网络连接错误，请检查您的网络连接。", "error_timeout": "请求超时，请重试。", "error_unauthorized": "未授权访问，请重新登录。", "error_forbidden": "访问被禁止。", "error_not_found": "资源未找到。", "error_server_error": "服务器错误，请稍后重试。", "error_validation_failed": "验证失败，请检查您的输入。", "validation_required": "此字段为必填项", "validation_email_invalid": "请输入有效的邮箱地址", "validation_password_too_short": "密码长度至少为 {minLength} 位", "validation_password_mismatch": "密码不匹配", "validation_phone_invalid": "请输入有效的手机号码", "light_theme": "浅色主题", "dark_theme": "深色主题", "system_theme": "跟随系统", "custom_theme": "自定义主题", "create_theme": "创建主题", "edit_theme": "编辑主题", "delete_theme": "删除主题", "theme_preview": "主题预览", "select_language": "选择语言", "current_language": "当前语言", "language_changed": "语言切换成功", "items_zero": "没有项目", "items_one": "1 个项目", "items_other": "{count} 个项目", "notifications_zero": "没有通知", "notifications_one": "1 条通知", "notifications_other": "{count} 条通知", "messages_zero": "没有消息", "messages_one": "1 条消息", "messages_other": "{count} 条消息"}